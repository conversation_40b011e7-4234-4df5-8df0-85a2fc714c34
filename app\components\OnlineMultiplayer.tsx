"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { ArrowLeft, Users, Copy, Check, Loader2, Crown, Wifi, AlertCircle } from "lucide-react"
import { useSocket } from "../hooks/useSocket"

interface OnlineMultiplayerProps {
  onBack: () => void
  onGameStart: (roomData: any) => void
}

export default function OnlineMultiplayer({ onBack, onGameStart }: OnlineMultiplayerProps) {
  const [playerName, setPlayerName] = useState("")
  const [roomCode, setRoomCode] = useState("")
  const [isCreatingRoom, setIsCreatingRoom] = useState(false)
  const [isJoiningRoom, setIsJoiningRoom] = useState(false)
  const [currentRoom, setCurrentRoom] = useState<any>(null)
  const [copied, setCopied] = useState(false)
  const [error, setError] = useState("")

  const { socket, isConnected } = useSocket()

  useEffect(() => {
    if (!socket) return

    socket.on("roomCreated", (room: any) => {
      setCurrentRoom(room)
      setIsCreatingRoom(false)
      setError("")
    })

    socket.on("roomJoined", (room: any) => {
      setCurrentRoom(room)
      setIsJoiningRoom(false)
      setError("")
    })

    socket.on("playerJoined", (room: any) => {
      setCurrentRoom(room)
    })

    socket.on("playerLeft", (room: any) => {
      setCurrentRoom(room)
    })

    socket.on("gameStarted", (gameData: any) => {
      onGameStart(gameData)
    })

    socket.on("error", (errorMessage: string) => {
      setError(errorMessage)
      setIsCreatingRoom(false)
      setIsJoiningRoom(false)
    })

    return () => {
      socket.off("roomCreated")
      socket.off("roomJoined")
      socket.off("playerJoined")
      socket.off("playerLeft")
      socket.off("gameStarted")
      socket.off("error")
    }
  }, [socket, onGameStart])

  const createRoom = () => {
    if (!playerName.trim()) {
      setError("Please enter your name")
      return
    }

    setIsCreatingRoom(true)
    setError("")
    socket?.emit("createRoom", { playerName: playerName.trim() })
  }

  const joinRoom = () => {
    if (!playerName.trim()) {
      setError("Please enter your name")
      return
    }

    if (!roomCode.trim()) {
      setError("Please enter a room code")
      return
    }

    setIsJoiningRoom(true)
    setError("")
    socket?.emit("joinRoom", { roomCode: roomCode.trim().toUpperCase(), playerName: playerName.trim() })
  }

  const leaveRoom = () => {
    socket?.emit("leaveRoom")
    setCurrentRoom(null)
  }

  const startGame = () => {
    socket?.emit("startGame")
  }

  const copyRoomCode = async () => {
    if (currentRoom?.code) {
      try {
        await navigator.clipboard.writeText(currentRoom.code)
        setCopied(true)
        setTimeout(() => setCopied(false), 2000)
      } catch (err) {
        // Fallback for browsers that don't support clipboard API
        const textArea = document.createElement("textarea")
        textArea.value = currentRoom.code
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand("copy")
        document.body.removeChild(textArea)
        setCopied(true)
        setTimeout(() => setCopied(false), 2000)
      }
    }
  }

  if (!isConnected) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center p-4">
        <Card className="p-8 text-center max-w-md w-full">
          <AlertCircle className="mx-auto mb-4 text-yellow-500" size={48} />
          <h2 className="text-2xl font-bold mb-4">Demo Mode</h2>
          <p className="text-gray-600 mb-4">
            Online multiplayer is running in demo mode. In a production environment, this would connect to a real
            Socket.io server.
          </p>
          <div className="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500">
            <p className="text-sm text-blue-800">
              <strong>Note:</strong> This demo simulates the online multiplayer experience. Room creation and joining
              will work, but games will be simulated.
            </p>
          </div>
        </Card>
      </div>
    )
  }

  if (currentRoom) {
    const isHost = currentRoom.host === socket?.id
    const canStart = currentRoom.players.length >= 2 && isHost

    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-500 to-purple-600 p-4">
        <div className="max-w-2xl mx-auto">
          <div className="flex items-center mb-6">
            <Button variant="ghost" onClick={leaveRoom} className="text-white hover:bg-white/20 mr-4">
              <ArrowLeft className="mr-2" size={20} />
              Leave Room
            </Button>
            <h1 className="text-3xl font-bold text-white">Game Lobby</h1>
          </div>

          {/* Demo Notice */}
          <div className="bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2 text-yellow-100">
              <AlertCircle size={20} />
              <span className="font-semibold">Demo Mode Active</span>
            </div>
            <p className="text-yellow-100 text-sm mt-1">
              This is a demonstration of the online multiplayer lobby. In production, this would be a real multiplayer
              room.
            </p>
          </div>

          <div className="grid gap-6">
            {/* Room Info */}
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-2xl font-bold">Room: {currentRoom.code}</h2>
                <Button onClick={copyRoomCode} variant="outline" size="sm">
                  {copied ? <Check size={16} /> : <Copy size={16} />}
                  {copied ? "Copied!" : "Copy Code"}
                </Button>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500">
                <p className="text-blue-800 font-semibold">Share this room code with your friends:</p>
                <p className="text-2xl font-bold text-blue-900 mt-2">{currentRoom.code}</p>
              </div>
            </Card>

            {/* Players List */}
            <Card className="p-6">
              <h3 className="text-xl font-bold mb-4">
                Players ({currentRoom.players.length}/4)
                <Wifi className="inline ml-2 text-green-500" size={20} />
              </h3>

              <div className="space-y-3">
                {currentRoom.players.map((player: any, index: number) => (
                  <div key={player.id} className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold">
                        {player.name.charAt(0).toUpperCase()}
                      </div>
                      <div>
                        <div className="font-semibold">{player.name}</div>
                        <div className="text-sm text-gray-500">
                          {player.id === currentRoom.host && (
                            <span className="flex items-center gap-1">
                              <Crown size={14} className="text-yellow-500" />
                              Host
                            </span>
                          )}
                          {player.id === socket?.id && <span className="text-blue-600">You</span>}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                      <span className="text-sm text-green-600">Online</span>
                    </div>
                  </div>
                ))}

                {/* Empty slots */}
                {Array.from({ length: 4 - currentRoom.players.length }).map((_, index) => (
                  <div key={index} className="flex items-center bg-gray-100 p-3 rounded-lg border-2 border-dashed">
                    <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                      <Users size={20} className="text-gray-500" />
                    </div>
                    <div className="ml-3 text-gray-500">Waiting for player...</div>
                  </div>
                ))}
              </div>
            </Card>

            {/* Game Controls */}
            <Card className="p-6">
              <div className="space-y-4">
                {isHost ? (
                  <>
                    <div className="bg-green-50 p-4 rounded-lg border-l-4 border-green-500">
                      <p className="text-green-800 font-semibold">You are the host!</p>
                      <p className="text-sm text-green-700 mt-1">
                        You can start the game when at least 2 players have joined.
                      </p>
                    </div>

                    <Button
                      onClick={startGame}
                      disabled={!canStart}
                      className="w-full h-14 text-lg font-semibold bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600"
                    >
                      {canStart ? "Start Demo Game" : `Need ${2 - currentRoom.players.length} more player(s)`}
                    </Button>
                  </>
                ) : (
                  <div className="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500">
                    <p className="text-blue-800 font-semibold">Waiting for host to start the game...</p>
                    <p className="text-sm text-blue-700 mt-1">The host will start the game when everyone is ready.</p>
                  </div>
                )}

                <div className="text-center text-sm text-gray-500">
                  Game will start automatically when the host clicks "Start Game"
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center p-4">
      <Card className="w-full max-w-md p-8">
        <div className="flex items-center mb-6">
          <Button variant="ghost" onClick={onBack} className="mr-4">
            <ArrowLeft className="mr-2" size={20} />
            Back
          </Button>
          <h1 className="text-2xl font-bold">Online Multiplayer</h1>
        </div>

        <div className="space-y-6">
          {/* Demo Notice */}
          <div className="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500">
            <div className="flex items-center gap-2 text-blue-800">
              <AlertCircle size={16} />
              <span className="font-semibold">Demo Mode</span>
            </div>
            <p className="text-sm text-blue-700 mt-1">
              This demonstrates the online multiplayer interface. Room creation and joining are simulated.
            </p>
          </div>

          {/* Player Name */}
          <div>
            <label className="block text-sm font-medium mb-2">Your Name</label>
            <Input
              value={playerName}
              onChange={(e) => setPlayerName(e.target.value)}
              placeholder="Enter your name"
              maxLength={20}
            />
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
          )}

          {/* Create Room */}
          <div>
            <h3 className="font-semibold mb-3">Create New Room</h3>
            <Button
              onClick={createRoom}
              disabled={isCreatingRoom || !playerName.trim()}
              className="w-full h-12 bg-green-600 hover:bg-green-700"
            >
              {isCreatingRoom ? (
                <>
                  <Loader2 className="mr-2 animate-spin" size={16} />
                  Creating Room...
                </>
              ) : (
                "Create Demo Room"
              )}
            </Button>
            <p className="text-sm text-gray-600 mt-2">Create a simulated room with a demo room code</p>
          </div>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white text-gray-500">OR</span>
            </div>
          </div>

          {/* Join Room */}
          <div>
            <h3 className="font-semibold mb-3">Join Existing Room</h3>
            <div className="space-y-3">
              <Input
                value={roomCode}
                onChange={(e) => setRoomCode(e.target.value.toUpperCase())}
                placeholder="Enter room code (try: DEMO01)"
                maxLength={6}
              />
              <Button
                onClick={joinRoom}
                disabled={isJoiningRoom || !playerName.trim() || !roomCode.trim()}
                variant="outline"
                className="w-full h-12"
              >
                {isJoiningRoom ? (
                  <>
                    <Loader2 className="mr-2 animate-spin" size={16} />
                    Joining Room...
                  </>
                ) : (
                  "Join Demo Room"
                )}
              </Button>
            </div>
            <p className="text-sm text-gray-600 mt-2">Try entering "DEMO01" as a room code to test joining</p>
          </div>

          {/* Connection Status */}
          <div className="bg-green-50 p-3 rounded-lg border border-green-200">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-green-800 font-medium">Demo server ready</span>
            </div>
          </div>
        </div>
      </Card>
    </div>
  )
}
