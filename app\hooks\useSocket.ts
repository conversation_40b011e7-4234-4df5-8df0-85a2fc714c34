"use client"

import { useEffect, useState } from "react"
import type { Socket } from "socket.io-client"

export function useSocket() {
  const [socket, setSocket] = useState<Socket | null>(null)
  const [isConnected, setIsConnected] = useState(false)

  useEffect(() => {
    // For development, we'll simulate the socket connection
    // In production, this would connect to a real Socket.io server
    const mockSocket = {
      id: Math.random().toString(36).substr(2, 9),
      connected: true,
      emit: (event: string, data?: any) => {
        console.log("Mock socket emit:", event, data)

        // Simulate server responses
        setTimeout(() => {
          if (event === "createRoom") {
            const roomCode = Math.random().toString(36).substr(2, 6).toUpperCase()
            const room = {
              code: roomCode,
              host: mockSocket.id,
              players: [{ id: mockSocket.id, name: data.playerName }],
              gameStarted: false,
            }
            mockSocket.listeners["roomCreated"]?.(room)
          } else if (event === "joinRoom") {
            // Simulate joining a room
            const room = {
              code: data.roomCode,
              host: "other-player-id",
              players: [
                { id: "other-player-id", name: "Host Player" },
                { id: mockSocket.id, name: data.playerName },
              ],
              gameStarted: false,
            }
            mockSocket.listeners["roomJoined"]?.(room)
          }
        }, 500)
      },
      on: (event: string, callback: Function) => {
        mockSocket.listeners[event] = callback
      },
      off: (event: string) => {
        delete mockSocket.listeners[event]
      },
      disconnect: () => {
        mockSocket.connected = false
        setIsConnected(false)
      },
      listeners: {} as Record<string, Function>,
    } as any

    setSocket(mockSocket)
    setIsConnected(true)

    return () => {
      mockSocket.disconnect()
    }
  }, [])

  return { socket, isConnected }
}
