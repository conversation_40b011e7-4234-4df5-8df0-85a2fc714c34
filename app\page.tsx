"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Users, Bot, Play, HelpCircle, Settings, Wifi, AlertCircle } from "lucide-react"
import GameBoard from "./components/GameBoard"
import GameRules from "./components/GameRules"
import GameTutorial from "./components/GameTutorial"
import OnlineMultiplayer from "./components/OnlineMultiplayer"
import OnlineGameBoard from "./components/OnlineGameBoard"

export default function HomePage() {
  const [gameStarted, setGameStarted] = useState(false)
  const [showRules, setShowRules] = useState(false)
  const [showTutorial, setShowTutorial] = useState(false)
  const [showOnlineMultiplayer, setShowOnlineMultiplayer] = useState(false)
  const [gameMode, setGameMode] = useState<"single" | "online">("single")
  const [playerCount, setPlayerCount] = useState(2)
  const [roomData, setRoomData] = useState<any>(null)

  if (gameStarted && gameMode === "single") {
    return <GameBoard gameMode={gameMode} playerCount={playerCount} onExit={() => setGameStarted(false)} />
  }

  if (gameStarted && gameMode === "online" && roomData) {
    return <OnlineGameBoard roomData={roomData} onExit={() => setGameStarted(false)} />
  }

  if (showOnlineMultiplayer) {
    return (
      <OnlineMultiplayer
        onBack={() => setShowOnlineMultiplayer(false)}
        onGameStart={(data) => {
          setRoomData(data)
          setGameMode("online")
          setGameStarted(true)
        }}
      />
    )
  }

  if (showRules) {
    return <GameRules onClose={() => setShowRules(false)} />
  }

  if (showTutorial) {
    return (
      <GameTutorial
        onClose={() => setShowTutorial(false)}
        onStartGame={() => {
          setShowTutorial(false)
          setGameMode("single")
          setGameStarted(true)
        }}
      />
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-500 via-yellow-500 to-blue-500 flex items-center justify-center p-4">
      <Card className="w-full max-w-lg p-8 bg-white/95 backdrop-blur-sm shadow-2xl">
        <div className="text-center mb-8">
          <div className="text-6xl font-bold bg-gradient-to-r from-red-600 to-blue-600 bg-clip-text text-transparent mb-4">
            UNO
          </div>
          <p className="text-gray-600 text-lg">The Classic Card Matching Game</p>
          <p className="text-sm text-gray-500 mt-2">Match colors, numbers, or play action cards to win!</p>
        </div>

        <div className="space-y-6">
          {/* Quick Start Section */}
          <div className="bg-blue-50 rounded-lg p-4 border-l-4 border-blue-500">
            <h3 className="font-semibold text-blue-800 mb-2">🎯 Quick Start</h3>
            <p className="text-sm text-blue-700">
              New to UNO? Start with the tutorial to learn the basics, or jump right into a game!
            </p>
          </div>

          {/* Game Mode Selection */}
          <div>
            <h3 className="text-lg font-semibold mb-3 flex items-center">
              <span className="bg-green-100 text-green-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-2">
                1
              </span>
              Choose Game Mode
            </h3>
            <div className="grid grid-cols-1 gap-3">
              <Button
                variant={gameMode === "single" ? "default" : "outline"}
                onClick={() => setGameMode("single")}
                className="flex items-center justify-between h-16 px-4"
              >
                <div className="flex items-center gap-3">
                  <Bot size={24} />
                  <div className="text-left">
                    <div className="font-semibold">Practice vs AI</div>
                    <div className="text-sm opacity-80">Play offline against computer opponents</div>
                  </div>
                </div>
                <div className="text-green-600 font-bold">Ready to Play</div>
              </Button>
              <Button
                variant={gameMode === "online" ? "default" : "outline"}
                onClick={() => {
                  setGameMode("online")
                  setShowOnlineMultiplayer(true)
                }}
                className="flex items-center justify-between h-16 px-4"
              >
                <div className="flex items-center gap-3">
                  <div className="relative">
                    <Users size={24} />
                    <Wifi size={12} className="absolute -top-1 -right-1 text-blue-500" />
                  </div>
                  <div className="text-left">
                    <div className="font-semibold">Online Multiplayer</div>
                    <div className="text-sm opacity-80">Demo of online features and room system</div>
                  </div>
                </div>
                <div className="text-blue-600 font-bold">DEMO</div>
              </Button>
            </div>
          </div>

          {/* Online Demo Notice */}
          {gameMode === "online" && (
            <div className="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500">
              <div className="flex items-center gap-2 text-blue-800 mb-2">
                <AlertCircle size={16} />
                <span className="font-semibold">Online Multiplayer Demo</span>
              </div>
              <p className="text-sm text-blue-700">
                Experience the online multiplayer interface, room creation, and lobby system. This demonstrates how the
                full online features would work.
              </p>
            </div>
          )}

          {/* Player Count for Single Player */}
          {gameMode === "single" && (
            <div>
              <h3 className="text-lg font-semibold mb-3 flex items-center">
                <span className="bg-green-100 text-green-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-2">
                  2
                </span>
                Number of AI Opponents
              </h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <span className="font-medium">Total Players: {playerCount}</span>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPlayerCount(Math.max(2, playerCount - 1))}
                      disabled={playerCount <= 2}
                    >
                      -
                    </Button>
                    <span className="w-8 text-center font-bold">{playerCount}</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPlayerCount(Math.min(4, playerCount + 1))}
                      disabled={playerCount >= 4}
                    >
                      +
                    </Button>
                  </div>
                </div>
                <div className="text-sm text-gray-600">
                  You vs {playerCount - 1} AI opponent{playerCount > 2 ? "s" : ""}
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="space-y-3">
            {gameMode === "single" ? (
              <Button
                onClick={() => setGameStarted(true)}
                className="w-full h-14 text-lg font-semibold bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600"
              >
                <Play className="mr-2" size={24} />
                Start Single Player Game
              </Button>
            ) : (
              <Button
                onClick={() => setShowOnlineMultiplayer(true)}
                className="w-full h-14 text-lg font-semibold bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
              >
                <Users className="mr-2" size={24} />
                Try Online Demo
              </Button>
            )}

            <div className="grid grid-cols-2 gap-3">
              <Button
                onClick={() => setShowTutorial(true)}
                variant="outline"
                className="h-12 flex items-center justify-center gap-2"
              >
                <HelpCircle size={18} />
                Tutorial
              </Button>
              <Button
                onClick={() => setShowRules(true)}
                variant="outline"
                className="h-12 flex items-center justify-center gap-2"
              >
                <Settings size={18} />
                Rules
              </Button>
            </div>
          </div>

          {/* Game Info */}
          <div className="bg-yellow-50 rounded-lg p-4 border-l-4 border-yellow-500">
            <h4 className="font-semibold text-yellow-800 mb-2">🎮 How to Win</h4>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>• Match the top card by color, number, or symbol</li>
              <li>• Use action cards strategically (Skip, Reverse, +2)</li>
              <li>• Call "UNO" when you have one card left!</li>
              <li>• First player to play all cards wins</li>
            </ul>
          </div>

          {/* Features Info */}
          <div className="bg-purple-50 rounded-lg p-4 border-l-4 border-purple-500">
            <h4 className="font-semibold text-purple-800 mb-2">✨ Game Features</h4>
            <ul className="text-sm text-purple-700 space-y-1">
              <li>• Complete official UNO rules implementation</li>
              <li>• Smart AI opponents with strategic gameplay</li>
              <li>• Wild Draw Four challenge system</li>
              <li>• Multi-round scoring to 500 points</li>
              <li>• Online multiplayer demo with room system</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  )
}
