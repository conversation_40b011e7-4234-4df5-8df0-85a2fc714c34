import type { GameState, UnoCard, CardColor } from "../types/game"
import { getValidMoves, isValidWildDrawFour } from "./gameLogic"

export function playAITurn(
  gameState: GameState,
  playCard: (card: UnoCard, colorChoice?: CardColor) => void,
  drawCard: () => void,
  passTurn: () => void,
  callUno: (playerId: number) => void,
) {
  const currentPlayer = gameState.players[gameState.currentPlayer]
  const topCard = gameState.discardPile[gameState.discardPile.length - 1]
  const validMoves = getValidMoves(currentPlayer.hand, topCard, gameState.currentColor)

  // Call UNO if needed (when about to have 1 card)
  if (currentPlayer.hand.length === 2 && !currentPlayer.hasCalledUno) {
    callUno(currentPlayer.id)
  }

  if (validMoves.length === 0) {
    // No valid moves, draw a card
    drawCard()
    return
  }

  // Advanced AI strategy
  const nextPlayerIndex =
    (gameState.currentPlayer + gameState.direction + gameState.players.length) % gameState.players.length
  const nextPlayer = gameState.players[nextPlayerIndex]
  const nextPlayerCardCount = nextPlayer.hand.length

  // Prioritize cards based on strategy
  let cardToPlay: UnoCard

  // If next player has 1 card (UNO), prioritize action cards to disrupt them
  if (nextPlayerCardCount === 1) {
    const actionCards = validMoves.filter((card) => ["skip", "draw2", "wild-draw4"].includes(card.type))
    if (actionCards.length > 0) {
      cardToPlay = actionCards[0]
    } else {
      cardToPlay = validMoves[0]
    }
  } else {
    // Normal strategy: prioritize action cards, then matching color, then wild cards
    const actionCards = validMoves.filter((card) => ["skip", "reverse", "draw2"].includes(card.type))
    const colorMatches = validMoves.filter((card) => card.color === gameState.currentColor && card.type === "number")
    const wildCards = validMoves.filter((card) => card.color === "wild")

    if (actionCards.length > 0) {
      cardToPlay = actionCards[0]
    } else if (colorMatches.length > 0) {
      // Play highest number card to get rid of high-value cards
      cardToPlay = colorMatches.reduce((highest, current) =>
        (current.value || 0) > (highest.value || 0) ? current : highest,
      )
    } else if (validMoves.length > 0) {
      cardToPlay = validMoves[0]
    } else {
      drawCard()
      return
    }
  }

  // Validate Wild Draw Four before playing
  if (cardToPlay.type === "wild-draw4" && !isValidWildDrawFour(currentPlayer.hand, gameState.currentColor)) {
    // Find alternative card
    const alternativeCards = validMoves.filter((card) => card.type !== "wild-draw4")
    if (alternativeCards.length > 0) {
      cardToPlay = alternativeCards[0]
    } else {
      drawCard()
      return
    }
  }

  // Choose color for wild cards intelligently
  let colorChoice: CardColor | undefined
  if (cardToPlay.color === "wild") {
    // Choose the most common color in hand, or a color that matches many cards
    const colorCounts = { red: 0, blue: 0, green: 0, yellow: 0 }
    currentPlayer.hand.forEach((card) => {
      if (card.color !== "wild") {
        colorCounts[card.color as keyof typeof colorCounts]++
      }
    })

    // If no colored cards, choose strategically based on what might hurt opponents
    if (Object.values(colorCounts).every((count) => count === 0)) {
      const colors: CardColor[] = ["red", "blue", "green", "yellow"]
      colorChoice = colors[Math.floor(Math.random() * colors.length)]
    } else {
      colorChoice = Object.entries(colorCounts).reduce((a, b) =>
        colorCounts[a[0] as keyof typeof colorCounts] > colorCounts[b[0] as keyof typeof colorCounts] ? a : b,
      )[0] as CardColor
    }
  }

  playCard(cardToPlay, colorChoice)
}
