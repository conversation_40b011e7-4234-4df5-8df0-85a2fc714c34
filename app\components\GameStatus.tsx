import type { GameState } from "../types/game"

interface GameStatusProps {
  gameState: GameState
}

export default function GameStatus({ gameState }: GameStatusProps) {
  const currentPlayer = gameState.players[gameState.currentPlayer]

  return (
    <div className="bg-white/10 rounded-lg p-4 mb-6 max-w-4xl mx-auto">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-white text-center">
        <div>
          <div className="text-sm opacity-80">Current Player</div>
          <div className="font-semibold text-lg">{currentPlayer.name}</div>
        </div>

        <div>
          <div className="text-sm opacity-80">Direction</div>
          <div className="font-semibold text-lg">
            {gameState.direction === 1 ? "→ Clockwise" : "← Counter-clockwise"}
          </div>
        </div>

        <div>
          <div className="text-sm opacity-80">Cards Left</div>
          <div className="font-semibold text-lg">{gameState.deck.length}</div>
        </div>
      </div>
    </div>
  )
}
