"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"

interface StackingModalProps {
  playerName: string
  stackType: "draw2" | "draw4"
  currentPenalty: number
  hasStackCard: boolean
  onStack: () => void
  onDraw: () => void
}

export default function StackingModal({
  playerName,
  stackType,
  currentPenalty,
  hasStackCard,
  onStack,
  onDraw,
}: StackingModalProps) {
  const cardName = stackType === "draw2" ? "Draw 2" : "Wild Draw 4"
  const penaltyText = stackType === "draw2" ? `${currentPenalty} cards` : `${currentPenalty} cards`

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="p-6 max-w-md w-full bg-white">
        <h2 className="text-xl font-bold mb-4 text-center">
          {stackType === "draw2" ? "Draw 2 Played!" : "Wild Draw 4 Played!"}
        </h2>
        
        <div className="text-center mb-6">
          <p className="text-gray-700 mb-2">
            <strong>{playerName}</strong>, you must:
          </p>
          <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
            <p className="text-red-800 font-semibold">
              Draw {penaltyText} and lose your turn
            </p>
            <p className="text-sm text-red-600 mt-1">
              OR
            </p>
            <p className="text-red-800 font-semibold">
              Play a {cardName} to stack the penalty
            </p>
          </div>
          
          {!hasStackCard && (
            <p className="text-gray-600 text-sm">
              You don't have a {cardName} card to stack.
            </p>
          )}
        </div>

        <div className="flex gap-3">
          <Button
            onClick={onDraw}
            variant="outline"
            className="flex-1 border-red-300 text-red-700 hover:bg-red-50"
          >
            Draw {currentPenalty} Cards
          </Button>
          
          {hasStackCard && (
            <Button
              onClick={onStack}
              className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
            >
              Stack {cardName}
            </Button>
          )}
        </div>
        
        {hasStackCard && (
          <p className="text-xs text-gray-500 mt-2 text-center">
            Stacking will pass the penalty to the next player
          </p>
        )}
      </Card>
    </div>
  )
}
