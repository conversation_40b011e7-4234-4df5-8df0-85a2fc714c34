"use client"

import type { Player, UnoCard, CardColor } from "../types/game"
import { canPlayCard } from "../utils/gameLogic"
import UnoCardComponent from "./UnoCard"
import UnoButton from "./UnoButton"

interface PlayerHandProps {
  player: Player
  isCurrentPlayer: boolean
  onCardClick: (card: UnoCard) => void
  onUnoCall: () => void
  topCard: UnoCard
  currentColor: CardColor
  drawnCard?: UnoCard | null
}

export default function PlayerHand({
  player,
  isCurrentPlayer,
  onCardClick,
  onUnoCall,
  topCard,
  currentColor,
  drawnCard,
}: PlayerHandProps) {
  const playableCards = player.hand.filter((card) => canPlayCard(card, topCard, currentColor))

  return (
    <div className="w-full max-w-6xl">
      <div className="bg-white/10 rounded-lg p-3 md:p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="text-white">
            <span className="font-semibold text-lg">{player.name}</span>
            <span className="ml-3 text-sm opacity-80">{player.hand.length} cards</span>
            {isCurrentPlayer && (
              <span className="ml-3 bg-yellow-400 text-black px-2 py-1 rounded text-xs font-bold">YOUR TURN</span>
            )}
            {playableCards.length > 0 && isCurrentPlayer && (
              <span className="ml-3 text-green-300 text-sm">{playableCards.length} playable</span>
            )}
          </div>

          <div className="flex items-center gap-2">
            {player.hand.length === 1 && player.hasCalledUno && <div className="text-green-300 text-sm animate-pulse">🎯 UNO!</div>}
            {player.hand.length === 1 && !player.hasCalledUno && isCurrentPlayer && <UnoButton onClick={onUnoCall} />}
            {player.hand.length === 2 && <div className="text-yellow-300 text-sm animate-pulse">⚠️ Remember to call UNO!</div>}
          </div>
        </div>

        {/* Drawn Card Highlight */}
        {drawnCard && isCurrentPlayer && (
          <div className="mb-3 p-2 bg-blue-500/20 rounded-lg border border-blue-400">
            <div className="text-blue-200 text-sm text-center">
              Just drawn: {canPlayCard(drawnCard, topCard, currentColor) ? "Can be played!" : "Cannot be played"}
            </div>
          </div>
        )}

        {/* Cards Display */}
        <div className="overflow-x-auto">
          <div className="flex gap-2 pb-2" style={{ minWidth: `${player.hand.length * 60}px` }}>
            {player.hand.map((card, index) => {
              const isPlayable = isCurrentPlayer && canPlayCard(card, topCard, currentColor)
              const isDrawnCard =
                drawnCard &&
                card.color === drawnCard.color &&
                card.type === drawnCard.type &&
                card.value === drawnCard.value

              return (
                <div key={`${card.color}-${card.type}-${card.value}-${index}`} className="flex-shrink-0">
                  <div className={isDrawnCard ? "ring-2 ring-blue-400 rounded-lg" : ""}>
                    <UnoCardComponent
                      card={card}
                      onClick={() => onCardClick(card)}
                      isPlayable={isPlayable}
                      size="medium"
                    />
                  </div>
                  {isPlayable && (
                    <div className="text-center mt-1">
                      <div className="text-xs text-green-300 font-semibold">PLAYABLE</div>
                    </div>
                  )}
                  {isDrawnCard && (
                    <div className="text-center mt-1">
                      <div className="text-xs text-blue-300 font-semibold">JUST DRAWN</div>
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        </div>

        {/* Help Text */}
        {isCurrentPlayer && (
          <div className="mt-3 text-center">
            {playableCards.length > 0 ? (
              <p className="text-green-300 text-sm">✅ Click on a highlighted card to play it</p>
            ) : (
              <p className="text-yellow-300 text-sm">❌ No playable cards - click DRAW to draw a card</p>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
