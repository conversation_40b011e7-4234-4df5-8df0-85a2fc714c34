"use client"

import type { UnoCard } from "../types/game"

interface UnoCardProps {
  card: UnoCard
  onClick?: () => void
  isPlayable?: boolean
  isSelected?: boolean
  size?: "small" | "medium" | "large"
}

export default function UnoCardComponent({
  card,
  onClick,
  isPlayable = true,
  isSelected = false,
  size = "medium",
}: UnoCardProps) {
  const getCardColor = () => {
    switch (card.color) {
      case "red":
        return "bg-red-500 border-red-600"
      case "blue":
        return "bg-blue-500 border-blue-600"
      case "green":
        return "bg-green-500 border-green-600"
      case "yellow":
        return "bg-yellow-400 border-yellow-500"
      case "wild":
        return "bg-gradient-to-br from-red-500 via-yellow-400 via-green-500 to-blue-500 border-gray-600"
      default:
        return "bg-gray-500 border-gray-600"
    }
  }

  const getCardText = () => {
    if (card.type === "number") return card.value?.toString()
    if (card.type === "skip") return "SKIP"
    if (card.type === "reverse") return "↻"
    if (card.type === "draw2") return "+2"
    if (card.type === "wild") return "WILD"
    if (card.type === "wild-draw4") return "+4"
    return ""
  }

  const getSizeClasses = () => {
    switch (size) {
      case "small":
        return "w-12 h-16 text-xs"
      case "large":
        return "w-24 h-32 text-xl"
      default:
        return "w-16 md:w-20 h-22 md:h-28 text-sm md:text-lg"
    }
  }

  return (
    <div
      className={`
        ${getSizeClasses()}
        ${getCardColor()}
        rounded-lg border-2
        flex items-center justify-center
        font-bold text-white
        transition-all duration-200
        relative
        ${isSelected ? "ring-4 ring-white transform -translate-y-2 shadow-2xl" : ""}
        ${isPlayable ? "cursor-pointer hover:scale-105 hover:shadow-lg hover:brightness-110" : "opacity-50 cursor-not-allowed"}
        ${onClick && isPlayable ? "hover:transform hover:-translate-y-1" : ""}
      `}
      onClick={isPlayable ? onClick : undefined}
    >
      <span className="drop-shadow-lg text-center leading-tight">{getCardText()}</span>

      {/* Playable indicator */}
      {isPlayable && onClick && (
        <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
      )}

      {/* Card shine effect */}
      <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-white/20 to-transparent opacity-0 hover:opacity-100 transition-opacity rounded-lg"></div>
    </div>
  )
}
