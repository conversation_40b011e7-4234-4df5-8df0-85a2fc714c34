"use client"

import Image from "next/image"
import type { UnoCard } from "../types/game"
import { getCardImagePath, getCardBackImagePath } from "../utils/cardImages"

interface UnoCardProps {
  card: UnoCard
  onClick?: () => void
  isPlayable?: boolean
  isSelected?: boolean
  size?: "small" | "medium" | "large"
  showBack?: boolean
}

export default function UnoCardComponent({
  card,
  onClick,
  isPlayable = true,
  isSelected = false,
  size = "medium",
  showBack = false,
}: UnoCardProps) {
  const getCardImageSrc = () => {
    if (showBack) {
      return getCardBackImagePath()
    }
    return getCardImagePath(card)
  }

  const getSizeClasses = () => {
    switch (size) {
      case "small":
        return "w-12 h-16"
      case "large":
        return "w-24 h-32"
      default:
        return "w-16 md:w-20 h-22 md:h-28"
    }
  }

  const getImageDimensions = () => {
    switch (size) {
      case "small":
        return { width: 48, height: 64 }
      case "large":
        return { width: 96, height: 128 }
      default:
        return { width: 80, height: 112 }
    }
  }

  const imageDimensions = getImageDimensions()

  return (
    <div
      className={`
        ${getSizeClasses()}
        rounded-lg
        transition-all duration-200
        relative
        ${isSelected ? "ring-4 ring-white transform -translate-y-2 shadow-2xl" : ""}
        ${isPlayable ? "cursor-pointer hover:scale-105 hover:shadow-lg" : "opacity-50 cursor-not-allowed"}
        ${onClick && isPlayable ? "hover:transform hover:-translate-y-1" : ""}
      `}
      onClick={isPlayable ? onClick : undefined}
    >
      <Image
        src={getCardImageSrc()}
        alt={showBack ? "Card back" : `${card.color} ${card.type} ${card.value || ""}`}
        width={imageDimensions.width}
        height={imageDimensions.height}
        className="rounded-lg object-contain w-full h-full"
        priority={false}
        unoptimized={true}
        style={{ aspectRatio: '2/3' }}
      />

      {/* Playable indicator */}
      {isPlayable && onClick && !showBack && (
        <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
      )}

      {/* Card shine effect */}
      <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-white/20 to-transparent opacity-0 hover:opacity-100 transition-opacity rounded-lg"></div>
    </div>
  )
}
