import type { Player } from "../types/game"

interface TurnIndicatorProps {
  currentPlayer: Player
}

export default function TurnIndicator({ currentPlayer }: TurnIndicatorProps) {
  return (
    <div className="flex items-center justify-center gap-2">
      <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
      <span className="text-sm font-medium">{currentPlayer.name}'s Turn</span>
      <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
    </div>
  )
}
