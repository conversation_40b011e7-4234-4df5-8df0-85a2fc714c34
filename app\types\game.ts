export type CardColor = "red" | "blue" | "green" | "yellow" | "wild"
export type CardType = "number" | "skip" | "reverse" | "draw2" | "wild" | "wild-draw4"

export interface UnoCard {
  color: CardColor
  type: CardType
  value?: number
}

export interface Player {
  id: number
  name: string
  hand: UnoCard[]
  isAI: boolean
  hasCalledUno: boolean
}

export interface GameState {
  players: Player[]
  deck: UnoCard[]
  discardPile: UnoCard[]
  currentPlayer: number
  direction: number // 1 for clockwise, -1 for counter-clockwise
  currentColor: CardColor
  drawCount: number
  gameStarted: boolean
}
