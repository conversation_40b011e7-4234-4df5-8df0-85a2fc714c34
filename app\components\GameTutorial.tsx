"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { ArrowLeft, ArrowRight, Play } from "lucide-react"
import UnoCardComponent from "./UnoCard"

interface GameTutorialProps {
  onClose: () => void
  onStartGame: () => void
}

export default function GameTutorial({ onClose, onStartGame }: GameTutorialProps) {
  const [currentStep, setCurrentStep] = useState(0)

  const tutorialSteps = [
    {
      title: "Welcome to UNO!",
      content: (
        <div className="text-center space-y-4">
          <div className="text-6xl">🎮</div>
          <p className="text-lg">Let's learn how to play UNO step by step!</p>
          <p className="text-gray-600">This tutorial will teach you everything you need to know.</p>
        </div>
      ),
    },
    {
      title: "The Goal",
      content: (
        <div className="space-y-4">
          <div className="text-center text-4xl">🎯</div>
          <p className="text-lg font-semibold text-center">Be the first to play all your cards!</p>
          <div className="bg-blue-50 p-4 rounded-lg">
            <p className="text-blue-800">
              You start with 7 cards. Play them by matching the top card's color, number, or symbol.
            </p>
          </div>
        </div>
      ),
    },
    {
      title: "Matching Cards",
      content: (
        <div className="space-y-4">
          <p className="text-lg font-semibold text-center">You can play a card if it matches:</p>
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="bg-red-50 p-3 rounded-lg">
              <div className="font-semibold text-red-800">Color</div>
              <div className="flex justify-center mt-2">
                <UnoCardComponent card={{ color: "red", type: "number", value: 5 }} size="small" />
              </div>
            </div>
            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="font-semibold text-blue-800">Number</div>
              <div className="flex justify-center mt-2">
                <UnoCardComponent card={{ color: "blue", type: "number", value: 7 }} size="small" />
              </div>
            </div>
            <div className="bg-green-50 p-3 rounded-lg">
              <div className="font-semibold text-green-800">Symbol</div>
              <div className="flex justify-center mt-2">
                <UnoCardComponent card={{ color: "green", type: "skip" }} size="small" />
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      title: "Special Action Cards",
      content: (
        <div className="space-y-4">
          <p className="text-lg font-semibold text-center">Action cards have special effects:</p>
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-yellow-50 p-3 rounded-lg text-center">
              <UnoCardComponent card={{ color: "yellow", type: "skip" }} size="small" />
              <div className="font-semibold text-yellow-800 mt-2">SKIP</div>
              <div className="text-sm text-yellow-700">Next player loses turn</div>
            </div>
            <div className="bg-red-50 p-3 rounded-lg text-center">
              <UnoCardComponent card={{ color: "red", type: "reverse" }} size="small" />
              <div className="font-semibold text-red-800 mt-2">REVERSE</div>
              <div className="text-sm text-red-700">Changes direction</div>
            </div>
            <div className="bg-blue-50 p-3 rounded-lg text-center">
              <UnoCardComponent card={{ color: "blue", type: "draw2" }} size="small" />
              <div className="font-semibold text-blue-800 mt-2">+2</div>
              <div className="text-sm text-blue-700">Next player draws 2</div>
            </div>
            <div className="bg-purple-50 p-3 rounded-lg text-center">
              <UnoCardComponent card={{ color: "wild", type: "wild-draw4" }} size="small" />
              <div className="font-semibold text-purple-800 mt-2">WILD +4</div>
              <div className="text-sm text-purple-700">Choose color + draw 4</div>
            </div>
          </div>
        </div>
      ),
    },
    {
      title: "The UNO Call",
      content: (
        <div className="space-y-4 text-center">
          <div className="text-4xl">🚨</div>
          <p className="text-lg font-semibold">Don't forget to call UNO!</p>
          <div className="bg-red-50 p-4 rounded-lg border-l-4 border-red-500">
            <p className="text-red-800 font-semibold">When you have ONE card left, you MUST click the "UNO!" button</p>
          </div>
          <div className="bg-orange-50 p-4 rounded-lg">
            <p className="text-orange-800">
              If you forget, other players can catch you and you'll draw 2 penalty cards!
            </p>
          </div>
        </div>
      ),
    },
    {
      title: "Ready to Play!",
      content: (
        <div className="text-center space-y-4">
          <div className="text-6xl">🎉</div>
          <p className="text-lg font-semibold">You're ready to play UNO!</p>
          <div className="bg-green-50 p-4 rounded-lg">
            <p className="text-green-800">
              Remember: Match colors, numbers, or symbols. Call UNO with one card left. Have fun!
            </p>
          </div>
          <Button onClick={onStartGame} size="lg" className="bg-green-600 hover:bg-green-700">
            <Play className="mr-2" size={20} />
            Start Playing Now!
          </Button>
        </div>
      ),
    },
  ]

  const nextStep = () => {
    if (currentStep < tutorialSteps.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl p-8">
        <div className="flex justify-between items-center mb-6">
          <Button variant="ghost" onClick={onClose}>
            <ArrowLeft className="mr-2" size={20} />
            Back
          </Button>
          <div className="text-sm text-gray-500">
            Step {currentStep + 1} of {tutorialSteps.length}
          </div>
        </div>

        <div className="mb-8">
          <h2 className="text-2xl font-bold mb-6 text-center">{tutorialSteps[currentStep].title}</h2>
          <div className="min-h-[300px] flex items-center justify-center">{tutorialSteps[currentStep].content}</div>
        </div>

        <div className="flex justify-between items-center">
          <Button onClick={prevStep} disabled={currentStep === 0} variant="outline">
            <ArrowLeft className="mr-2" size={16} />
            Previous
          </Button>

          <div className="flex space-x-2">
            {tutorialSteps.map((_, index) => (
              <div
                key={index}
                className={`w-3 h-3 rounded-full ${index === currentStep ? "bg-blue-500" : "bg-gray-300"}`}
              />
            ))}
          </div>

          {currentStep < tutorialSteps.length - 1 ? (
            <Button onClick={nextStep}>
              Next
              <ArrowRight className="ml-2" size={16} />
            </Button>
          ) : (
            <Button onClick={onStartGame} className="bg-green-600 hover:bg-green-700">
              <Play className="mr-2" size={16} />
              Play Now!
            </Button>
          )}
        </div>
      </Card>
    </div>
  )
}
