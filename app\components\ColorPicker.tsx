"use client"

import type { CardColor } from "../types/game"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"

interface ColorPickerProps {
  onColorSelect: (color: CardColor) => void
  onClose: () => void
}

export default function ColorPicker({ onColorSelect, onClose }: ColorPickerProps) {
  const colors: { color: CardColor; bg: string; name: string }[] = [
    { color: "red", bg: "bg-red-500", name: "Red" },
    { color: "blue", bg: "bg-blue-500", name: "Blue" },
    { color: "green", bg: "bg-green-500", name: "Green" },
    { color: "yellow", bg: "bg-yellow-400", name: "Yellow" },
  ]

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="p-6 max-w-sm w-full mx-4">
        <h3 className="text-xl font-bold text-center mb-6">Choose a Color</h3>

        <div className="grid grid-cols-2 gap-4 mb-6">
          {colors.map(({ color, bg, name }) => (
            <Button
              key={color}
              onClick={() => onColorSelect(color)}
              className={`${bg} hover:opacity-80 h-16 text-white font-semibold`}
            >
              {name}
            </Button>
          ))}
        </div>

        <Button onClick={onClose} variant="outline" className="w-full">
          Cancel
        </Button>
      </Card>
    </div>
  )
}
