import type { UnoCard } from "../types/game"

// Card image mapping based on the available images
// Using a simplified approach with UNO - 1.png to UNO - 36.png for all cards
export function getCardImagePath(card: UnoCard): string {
  // Handle wild cards
  if (card.color === "wild") {
    return "/uno-cards/UNO.png" // Main wild card for both wild and wild-draw4
  }

  // Create a systematic mapping based on standard UNO deck order
  // Red: 0-9, <PERSON><PERSON>, <PERSON>erse, Draw2 (1-13)
  // Yellow: 0-9, <PERSON><PERSON>, <PERSON><PERSON>, Draw2 (14-26)
  // Green: 0-9, <PERSON><PERSON>, <PERSON><PERSON>, Draw2 (27-39, but we only have up to 36)
  // Blue: Use remaining numbered cards (cycle back to 1-13 for now)
  const getCardIndex = (color: string, type: string, value?: number): number => {
    let baseOffset = 0

    switch (color) {
      case "red": baseOffset = 0; break
      case "yellow": baseOffset = 13; break
      case "green": baseOffset = 26; break
      case "blue": baseOffset = 0; break // Cycle back to red cards for blue
      default: baseOffset = 0
    }

    if (type === "number" && value !== undefined) {
      return baseOffset + value + 1 // +1 because files start at 1
    } else if (type === "skip") {
      return baseOffset + 11
    } else if (type === "reverse") {
      return baseOffset + 12
    } else if (type === "draw2") {
      return baseOffset + 13
    }
    return 1 // fallback
  }

  const cardIndex = getCardIndex(card.color, card.type, card.value)

  // Ensure we stay within available range (1-36)
  const finalIndex = Math.min(Math.max(cardIndex, 1), 36)
  return `/uno-cards/UNO - ${finalIndex}.png`
}



// Get card back image
export function getCardBackImagePath(): string {
  return "/uno-cards/UNO.png" // This is the official UNO card back
}

// Preload card images for better performance
export function preloadCardImages(): void {
  const imagePaths = [
    "/uno-cards/UNO.png",
    ...Array.from({ length: 36 }, (_, i) => `/uno-cards/UNO - ${i + 1}.png`),
    "/uno-cards/→ E.png.png",
    "/uno-cards/→ E.png(1).png",
    "/uno-cards/→ E.zip - '.png",
    "/uno-cards/→ E.zip - .png",
    ...Array.from({ length: 12 }, (_, i) => `/uno-cards/→ E.zip - ${i + 1}.png`),
  ]

  imagePaths.forEach((path) => {
    const img = new Image()
    img.src = path
  })
}
