import type { UnoCard } from "../types/game"

// Card image mapping based on the available images
// Assuming UNO - 1.png to UNO - 36.png contain the main deck in order:
// Red: 0-9, <PERSON><PERSON>, <PERSON><PERSON>, Draw2 (1-13)
// Yellow: 0-9, <PERSON><PERSON>, <PERSON><PERSON>, Draw2 (14-26)
// Green: 0-9, <PERSON><PERSON>, <PERSON><PERSON>, Draw2 (27-39, but we only have up to 36)
// Blue: remaining cards in the other files
export function getCardImagePath(card: UnoCard): string {
  // Handle wild cards
  if (card.color === "wild") {
    if (card.type === "wild") {
      return "/uno-cards/UNO.png" // Main wild card
    }
    if (card.type === "wild-draw4") {
      return "/uno-cards/UNO.png" // Use same image for now
    }
  }

  // Create a systematic mapping based on standard UNO deck order
  const getCardIndex = (color: string, type: string, value?: number): number => {
    const colorOffsets = { red: 0, yellow: 13, green: 26, blue: 39 }
    const baseOffset = colorOffsets[color as keyof typeof colorOffsets] || 0

    if (type === "number" && value !== undefined) {
      return baseOffset + value + 1 // +1 because files start at 1
    } else if (type === "skip") {
      return baseOffset + 11
    } else if (type === "reverse") {
      return baseOffset + 12
    } else if (type === "draw2") {
      return baseOffset + 13
    }
    return 1 // fallback
  }

  const cardIndex = getCardIndex(card.color, card.type, card.value)

  // For cards 1-36, use the numbered files
  if (cardIndex <= 36) {
    return `/uno-cards/UNO - ${cardIndex}.png`
  }

  // For blue cards (which go beyond 36), use the alternative files
  if (card.color === "blue") {
    if (card.type === "number" && card.value !== undefined) {
      const blueNumberFiles = [
        "/uno-cards/→ E.zip - 5.png",  // Blue 0
        "/uno-cards/→ E.zip - 7.png",  // Blue 1
        "/uno-cards/→ E.zip - 8.png",  // Blue 2
        "/uno-cards/→ E.zip - 9.png",  // Blue 3
        "/uno-cards/→ E.zip - 10.png", // Blue 4
        "/uno-cards/→ E.zip - 11.png", // Blue 5
        "/uno-cards/→ E.zip - 12.png", // Blue 6
        "/uno-cards/→ E.png.png",      // Blue 7
        "/uno-cards/→ E.png(1).png",   // Blue 8
        "/uno-cards/→ E.zip - '.png",  // Blue 9
      ]
      return blueNumberFiles[card.value] || "/uno-cards/UNO.png"
    } else if (card.type === "skip") {
      return "/uno-cards/→ E.zip - 1.png"
    } else if (card.type === "reverse") {
      return "/uno-cards/→ E.zip - 3.png"
    } else if (card.type === "draw2") {
      return "/uno-cards/→ E.zip - 4.png"
    }
  }

  // Fallback
  return "/uno-cards/UNO.png"
}



// Get card back image
export function getCardBackImagePath(): string {
  return "/uno-cards/UNO.png" // Using the main UNO card as back
}

// Preload card images for better performance
export function preloadCardImages(): void {
  const imagePaths = [
    "/uno-cards/UNO.png",
    ...Array.from({ length: 36 }, (_, i) => `/uno-cards/UNO - ${i + 1}.png`),
    "/uno-cards/→ E.png.png",
    "/uno-cards/→ E.png(1).png",
    "/uno-cards/→ E.zip - '.png",
    "/uno-cards/→ E.zip - .png",
    ...Array.from({ length: 12 }, (_, i) => `/uno-cards/→ E.zip - ${i + 1}.png`),
  ]

  imagePaths.forEach((path) => {
    const img = new Image()
    img.src = path
  })
}
