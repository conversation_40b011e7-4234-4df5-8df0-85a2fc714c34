import type { UnoCard } from "../types/game"

// Card image mapping based on the new card names
export function getCardImagePath(card: UnoCard): string {
  // Handle wild cards
  if (card.color === "wild") {
    if (card.type === "wild") {
      return "/uno-cards/wild.png"
    }
    if (card.type === "wild-draw4") {
      return "/uno-cards/Draw four.png"
    }
  }

  // Handle colored cards with new naming convention
  const color = card.color.toLowerCase()

  if (card.type === "number" && card.value !== undefined) {
    // Map numbers to word equivalents
    const numberWords = {
      0: "zero", // Note: no zero cards in your set, will fallback
      1: "one",
      2: "two",
      3: "three",
      4: "four",
      5: "five",
      6: "six",
      7: "seven",
      8: "eight",
      9: "nine"
    }

    const numberWord = numberWords[card.value as keyof typeof numberWords]
    if (numberWord && numberWord !== "zero") {
      return `/uno-cards/${color} ${numberWord}.png`
    }
    // Fallback for 0 or missing numbers
    return `/uno-cards/${color} one.png`
  }

  // Handle action cards
  if (card.type === "skip") {
    return `/uno-cards/${color} skip.png`
  }
  if (card.type === "reverse") {
    return `/uno-cards/${color} reverse.png`
  }
  if (card.type === "draw2") {
    // Handle different naming conventions for draw two cards
    if (color === "green") {
      return `/uno-cards/Green Draw two.png` // Capital G
    } else {
      return `/uno-cards/${color} draw two.png`
    }
  }

  // Fallback
  return "/uno-cards/wild.png"
}



// Get card back image
export function getCardBackImagePath(): string {
  return "/uno-cards/Uno back card.png" // This is the official UNO card back
}

// Preload card images for better performance
export function preloadCardImages(): void {
  const colors = ["red", "blue", "green", "yellow"]
  const numbers = ["one", "two", "three", "four", "five", "six", "seven", "eight", "nine"]
  const actions = ["skip", "reverse", "draw two"]

  const imagePaths = [
    "/uno-cards/Uno back card.png",
    "/uno-cards/wild.png",
    "/uno-cards/Draw four.png",
  ]

  // Add all colored cards
  colors.forEach(color => {
    // Number cards
    numbers.forEach(number => {
      imagePaths.push(`/uno-cards/${color} ${number}.png`)
    })

    // Action cards
    actions.forEach(action => {
      if (color === "green" && action === "draw two") {
        imagePaths.push(`/uno-cards/Green Draw two.png`) // Capital G
      } else {
        imagePaths.push(`/uno-cards/${color} ${action}.png`)
      }
    })
  })

  imagePaths.forEach((path) => {
    const img = new Image()
    img.src = path
  })
}
