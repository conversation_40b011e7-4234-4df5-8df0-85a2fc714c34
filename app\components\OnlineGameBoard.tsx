"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { ArrowLeft, Users, MessageCircle, Wifi, AlertCircle, Play } from "lucide-react"
import { useSocket } from "../hooks/useSocket"

interface OnlineGameBoardProps {
  roomData: any
  onExit: () => void
}

export default function OnlineGameBoard({ roomData, onExit }: OnlineGameBoardProps) {
  const [gameState, setGameState] = useState<any>(null)
  const [chatMessages, setChatMessages] = useState<Array<{ player: string; message: string }>>([
    { player: "System", message: "Welcome to the UNO demo game!" },
    { player: "System", message: "Online multiplayer features are being demonstrated." },
  ])
  const [chatInput, setChatInput] = useState("")
  const { socket } = useSocket()

  useEffect(() => {
    if (!socket) return

    // Simulate some demo chat messages
    const demoMessages = [
      { player: "Host Player", message: "Hey everyone! Ready to play?" },
      { player: "Player 2", message: "Let's do this! 🎮" },
    ]

    setTimeout(() => {
      setChatMessages((prev) => [...prev, ...demoMessages])
    }, 2000)

    // Listen for game updates (simulated)
    socket.on("gameUpdate", (update: any) => {
      setGameState(update.gameState)
    })

    socket.on("chatMessage", (data: any) => {
      setChatMessages((prev) => [...prev, data])
    })

    return () => {
      socket.off("gameUpdate")
      socket.off("chatMessage")
    }
  }, [socket])

  const sendChatMessage = () => {
    if (chatInput.trim()) {
      const newMessage = {
        player: "You",
        message: chatInput.trim(),
      }
      setChatMessages((prev) => [...prev, newMessage])
      setChatInput("")

      // Simulate a response
      setTimeout(() => {
        setChatMessages((prev) => [...prev, { player: "Demo Bot", message: "Nice message! This is a demo response." }])
      }, 1000)
    }
  }

  const leaveGame = () => {
    if (socket) {
      socket.emit("leaveRoom")
    }
    onExit()
  }

  const startSinglePlayerDemo = () => {
    // Redirect to single player game
    onExit()
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-500 to-pink-500 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-6 bg-white/10 rounded-lg p-4">
          <Button variant="ghost" onClick={leaveGame} className="text-white hover:bg-white/20">
            <ArrowLeft className="mr-2" size={20} />
            Leave Game
          </Button>

          <div className="text-white text-center">
            <h1 className="text-2xl font-bold">UNO Online Demo</h1>
            <div className="flex items-center justify-center gap-2 mt-1">
              <Wifi size={16} className="text-green-400" />
              <span className="text-sm">Room: {roomData.roomCode}</span>
            </div>
          </div>

          <div className="flex items-center gap-2 text-white">
            <Users size={20} />
            <span>{roomData.players?.length || 0}/4</span>
          </div>
        </div>

        {/* Demo Notice */}
        <div className="bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-4 mb-6">
          <div className="flex items-center gap-2 text-yellow-100">
            <AlertCircle size={20} />
            <span className="font-semibold">Online Multiplayer Demo</span>
          </div>
          <p className="text-yellow-100 text-sm mt-1">
            This demonstrates the online game interface. The full multiplayer functionality would include real-time card
            playing, synchronized game state, and live player interactions.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Game Area */}
          <div className="lg:col-span-3">
            <Card className="p-8 text-center min-h-[600px] flex items-center justify-center">
              <div>
                <div className="text-6xl mb-4">🎮</div>
                <h2 className="text-2xl font-bold mb-4">Online UNO Game</h2>
                <p className="text-gray-600 mb-6">
                  You're connected to room <strong>{roomData.roomCode}</strong> with {roomData.players?.length || 0}{" "}
                  players. In a full implementation, you would see the game board, cards, and real-time gameplay here.
                </p>

                <div className="bg-blue-50 p-6 rounded-lg border-l-4 border-blue-500 mb-6 text-left">
                  <h3 className="font-semibold text-blue-800 mb-3">🚀 Full Online Features (Coming Soon):</h3>
                  <ul className="text-sm text-blue-700 space-y-2">
                    <li>
                      • <strong>Real-time card playing</strong> - Play cards instantly with other players
                    </li>
                    <li>
                      • <strong>Synchronized game state</strong> - Everyone sees the same game state
                    </li>
                    <li>
                      • <strong>Turn-based gameplay</strong> - Proper turn management across players
                    </li>
                    <li>
                      • <strong>Live chat during games</strong> - Chat with other players while playing
                    </li>
                    <li>
                      • <strong>Reconnection support</strong> - Rejoin games if you lose connection
                    </li>
                    <li>
                      • <strong>Spectator mode</strong> - Watch ongoing games
                    </li>
                    <li>
                      • <strong>Player statistics</strong> - Track wins, losses, and achievements
                    </li>
                  </ul>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button onClick={startSinglePlayerDemo} className="h-12">
                    <Play className="mr-2" size={20} />
                    Try Single Player Game
                  </Button>
                  <Button onClick={leaveGame} variant="outline" className="h-12">
                    Back to Menu
                  </Button>
                </div>
              </div>
            </Card>
          </div>

          {/* Chat & Players Sidebar */}
          <div className="space-y-6">
            {/* Players List */}
            <Card className="p-4">
              <h3 className="font-bold mb-3 flex items-center gap-2">
                <Users size={18} />
                Players Online
              </h3>
              <div className="space-y-2">
                {roomData.players?.map((player: any, index: number) => (
                  <div key={player.id} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                    <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                      {player.name.charAt(0).toUpperCase()}
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-sm">{player.name}</div>
                      <div className="flex items-center gap-1">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-xs text-green-600">Online</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* Chat */}
            <Card className="p-4">
              <h3 className="font-bold mb-3 flex items-center gap-2">
                <MessageCircle size={18} />
                Chat
              </h3>

              <div className="h-48 bg-gray-50 rounded p-3 mb-3 overflow-y-auto">
                <div className="space-y-2">
                  {chatMessages.map((msg, index) => (
                    <div key={index} className="text-sm">
                      <span
                        className={`font-semibold ${msg.player === "System" ? "text-gray-600" : msg.player === "You" ? "text-green-600" : "text-blue-600"}`}
                      >
                        {msg.player}:
                      </span>
                      <span className="ml-2">{msg.message}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex gap-2">
                <Input
                  value={chatInput}
                  onChange={(e) => setChatInput(e.target.value)}
                  onKeyPress={(e) => e.key === "Enter" && sendChatMessage()}
                  placeholder="Type a message..."
                  className="flex-1 text-sm"
                  maxLength={100}
                />
                <Button onClick={sendChatMessage} size="sm" disabled={!chatInput.trim()}>
                  Send
                </Button>
              </div>
              <p className="text-xs text-gray-500 mt-2">Try sending a message to see the demo chat in action!</p>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
