import type { NextRequest } from "next/server"
import { Server as SocketIOServer } from "socket.io"
import { Server as HTTPServer } from "http"

interface Room {
  code: string
  host: string
  players: Array<{
    id: string
    name: string
  }>
  gameState?: any
  gameStarted: boolean
}

const rooms = new Map<string, Room>()

function generateRoomCode(): string {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
  let result = ""
  for (let i = 0; i < 6; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

export async function GET(req: NextRequest) {
  if (!(global as any).io) {
    const httpServer = new HTTPServer()
    const io = new SocketIOServer(httpServer, {
      path: "/api/socket",
      addTrailingSlash: false,
      cors: {
        origin: "*",
        methods: ["GET", "POST"],
      },
    })

    io.on("connection", (socket) => {
      console.log("Client connected:", socket.id)

      socket.on("createRoom", ({ playerName }) => {
        let roomCode = generateRoomCode()
        while (rooms.has(roomCode)) {
          roomCode = generateRoomCode()
        }

        const room: Room = {
          code: roomCode,
          host: socket.id,
          players: [{ id: socket.id, name: playerName }],
          gameStarted: false,
        }

        rooms.set(roomCode, room)
        socket.join(roomCode)

        socket.emit("roomCreated", room)
        console.log(`Room ${roomCode} created by ${playerName}`)
      })

      socket.on("joinRoom", ({ roomCode, playerName }) => {
        const room = rooms.get(roomCode)

        if (!room) {
          socket.emit("error", "Room not found")
          return
        }

        if (room.gameStarted) {
          socket.emit("error", "Game already started")
          return
        }

        if (room.players.length >= 4) {
          socket.emit("error", "Room is full")
          return
        }

        if (room.players.some((p) => p.name === playerName)) {
          socket.emit("error", "Name already taken in this room")
          return
        }

        room.players.push({ id: socket.id, name: playerName })
        socket.join(roomCode)

        socket.emit("roomJoined", room)
        socket.to(roomCode).emit("playerJoined", room)

        console.log(`${playerName} joined room ${roomCode}`)
      })

      socket.on("leaveRoom", () => {
        for (const [roomCode, room] of rooms.entries()) {
          const playerIndex = room.players.findIndex((p) => p.id === socket.id)

          if (playerIndex !== -1) {
            room.players.splice(playerIndex, 1)
            socket.leave(roomCode)

            if (room.players.length === 0) {
              rooms.delete(roomCode)
              console.log(`Room ${roomCode} deleted - no players left`)
            } else {
              // If host left, assign new host
              if (room.host === socket.id && room.players.length > 0) {
                room.host = room.players[0].id
              }

              socket.to(roomCode).emit("playerLeft", room)
              console.log(`Player left room ${roomCode}`)
            }
            break
          }
        }
      })

      socket.on("startGame", () => {
        for (const [roomCode, room] of rooms.entries()) {
          if (room.host === socket.id && room.players.length >= 2) {
            room.gameStarted = true

            const gameData = {
              roomCode,
              players: room.players,
              gameStarted: true,
            }

            io.to(roomCode).emit("gameStarted", gameData)
            console.log(`Game started in room ${roomCode}`)
            break
          }
        }
      })

      socket.on("gameAction", (data) => {
        const { roomCode, action, payload } = data
        const room = rooms.get(roomCode)

        if (room && room.gameStarted) {
          // Update game state based on action
          // This will be expanded with actual game logic
          socket.to(roomCode).emit("gameUpdate", { action, payload, playerId: socket.id })
        }
      })

      socket.on("disconnect", () => {
        console.log("Client disconnected:", socket.id)

        // Remove player from all rooms
        for (const [roomCode, room] of rooms.entries()) {
          const playerIndex = room.players.findIndex((p) => p.id === socket.id)

          if (playerIndex !== -1) {
            room.players.splice(playerIndex, 1)

            if (room.players.length === 0) {
              rooms.delete(roomCode)
            } else {
              if (room.host === socket.id && room.players.length > 0) {
                room.host = room.players[0].id
              }
              socket.to(roomCode).emit("playerLeft", room)
            }
            break
          }
        }
      })
    })
    ;(global as any).io = io
  }

  return new Response("Socket.IO server initialized", { status: 200 })
}
