"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { X } from "lucide-react"

interface GameInstructionsProps {
  onClose: () => void
}

export default function GameInstructions({ onClose }: GameInstructionsProps) {
  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold">How to Play UNO</h2>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X size={20} />
            </Button>
          </div>

          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-3 text-green-600">🎯 Objective</h3>
              <p className="text-gray-700">Be the first player to play all your cards!</p>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-3 text-blue-600">🃏 How to Play</h3>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start gap-2">
                  <span className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold flex-shrink-0 mt-0.5">
                    1
                  </span>
                  <span>
                    Match the top card by <strong>color</strong>, <strong>number</strong>, or <strong>symbol</strong>
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold flex-shrink-0 mt-0.5">
                    2
                  </span>
                  <span>If you can't play, click "DRAW" to draw a card</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold flex-shrink-0 mt-0.5">
                    3
                  </span>
                  <span>When you have one card left, click "UNO!" button</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold flex-shrink-0 mt-0.5">
                    4
                  </span>
                  <span>Play your last card to win!</span>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-3 text-red-600">⚡ Special Cards</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-red-50 p-3 rounded-lg">
                  <div className="font-semibold text-red-800">SKIP</div>
                  <div className="text-sm text-red-700">Next player loses their turn</div>
                </div>
                <div className="bg-blue-50 p-3 rounded-lg">
                  <div className="font-semibold text-blue-800">REVERSE</div>
                  <div className="text-sm text-blue-700">Changes direction of play</div>
                </div>
                <div className="bg-green-50 p-3 rounded-lg">
                  <div className="font-semibold text-green-800">+2</div>
                  <div className="text-sm text-green-700">Next player draws 2 cards</div>
                </div>
                <div className="bg-purple-50 p-3 rounded-lg">
                  <div className="font-semibold text-purple-800">WILD</div>
                  <div className="text-sm text-purple-700">Choose any color</div>
                </div>
                <div className="bg-yellow-50 p-3 rounded-lg md:col-span-2">
                  <div className="font-semibold text-yellow-800">WILD +4</div>
                  <div className="text-sm text-yellow-700">Choose color + next player draws 4 cards</div>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-3 text-yellow-600">⚠️ Important Rules</h3>
              <ul className="space-y-2 text-gray-700">
                <li>• You MUST call "UNO" when you have one card left</li>
                <li>• If you forget to call UNO, other players can catch you</li>
                <li>• Wild +4 can only be played if you have no matching color</li>
                <li>• You can challenge a Wild +4 if you think it was played illegally</li>
              </ul>
            </div>

            <div className="bg-green-50 p-4 rounded-lg border-l-4 border-green-500">
              <h4 className="font-semibold text-green-800 mb-2">💡 Pro Tips</h4>
              <ul className="text-sm text-green-700 space-y-1">
                <li>• Save action cards for strategic moments</li>
                <li>• Pay attention to other players' card counts</li>
                <li>• Use Wild cards wisely to change to your strongest color</li>
                <li>• Watch for players who forget to call UNO!</li>
              </ul>
            </div>
          </div>

          <div className="mt-6 pt-6 border-t">
            <Button onClick={onClose} className="w-full">
              Got it! Let's Play
            </Button>
          </div>
        </div>
      </Card>
    </div>
  )
}
