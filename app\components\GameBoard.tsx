"use client"

import { useState, useEffect, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { ArrowLeft, Volume2, VolumeX, HelpCircle, RotateCcw } from "lucide-react"
import type { GameState, Player, UnoCard, CardColor } from "../types/game"
import { createDeck, shuffleDeck, canPlayCard, isValidWildDrawFour, calculateScore } from "../utils/gameLogic"
import { playAITurn } from "../utils/aiLogic"
import { preloadCardImages } from "../utils/cardImages"
import UnoCardComponent from "./UnoCard"
import PlayerHand from "./PlayerHand"
import GameStatus from "./GameStatus"
import ColorPicker from "./ColorPicker"
import GameInstructions from "./GameInstructions"
import TurnIndicator from "./TurnIndicator"
import ChallengeModal from "./ChallengeModal"
import ScoreBoard from "./ScoreBoard"

interface GameBoardProps {
  gameMode: "single" | "online"
  playerCount?: number
  roomData?: any
  onExit: () => void
}

export default function GameBoard({ gameMode, playerCount = 2, roomData, onExit }: GameBoardProps) {
  const [gameState, setGameState] = useState<GameState | null>(null)
  const [selectedCard, setSelectedCard] = useState<UnoCard | null>(null)
  const [showColorPicker, setShowColorPicker] = useState(false)
  const [soundEnabled, setSoundEnabled] = useState(true)
  const [gameOver, setGameOver] = useState(false)
  const [winner, setWinner] = useState<Player | null>(null)
  const [showInstructions, setShowInstructions] = useState(false)
  const [gameMessage, setGameMessage] = useState<string>("")
  const [lastAction, setLastAction] = useState<string>("")
  const [showChallenge, setShowChallenge] = useState(false)
  const [challengeContext, setChallengeContext] = useState<{
    challengerId: number
    playerId: number
    card: UnoCard
  } | null>(null)
  const [scores, setScores] = useState<number[]>([])
  const [roundNumber, setRoundNumber] = useState(1)
  const [targetScore] = useState(500)
  const [drawnCardThisTurn, setDrawnCardThisTurn] = useState<UnoCard | null>(null)
  const [canPlayDrawnCard, setCanPlayDrawnCard] = useState(false)

  // Single player game logic (existing code)
  useEffect(() => {
    initializeGame()
  }, [playerCount])

  // If online mode, use the online game board
  useEffect(() => {
    if (gameMode === "online" && roomData) {
      setGameMessage("Online Game Coming Soon!")
      setGameOver(true)
    }
  }, [gameMode, roomData])

  // Preload card images for better performance
  useEffect(() => {
    preloadCardImages()
  }, [])

  const initializeGame = () => {
    const deck = shuffleDeck(createDeck())
    const players: Player[] = []

    // Create players
    for (let i = 0; i < playerCount; i++) {
      players.push({
        id: i,
        name: i === 0 ? "You" : `AI Player ${i}`,
        hand: deck.splice(0, 7),
        isAI: i > 0,
        hasCalledUno: false,
      })
    }

    // Determine starting player (random, as per UNO rules)
    const startingPlayer = Math.floor(Math.random() * playerCount)

    let topCard = deck.pop()!

    // Handle special starting cards
    while (topCard.color === "wild") {
      deck.push(topCard)
      deck.splice(Math.floor(Math.random() * deck.length), 0, topCard)
      topCard = deck.pop()!
    }

    const initialGameState: GameState = {
      players,
      deck,
      discardPile: [topCard],
      currentPlayer: startingPlayer,
      direction: 1,
      currentColor: topCard.color === "wild" ? "red" : topCard.color,
      drawCount: 0,
      gameStarted: true,
    }

    // Handle starting action cards
    if (topCard.type === "skip") {
      initialGameState.currentPlayer = (startingPlayer + 1) % playerCount
      setLastAction("Starting card is SKIP - first player's turn is skipped!")
    } else if (topCard.type === "reverse") {
      initialGameState.direction = -1
      initialGameState.currentPlayer = startingPlayer === 0 ? playerCount - 1 : startingPlayer - 1
      setLastAction("Starting card is REVERSE - direction reversed!")
    } else if (topCard.type === "draw2") {
      const drawnCards = initialGameState.deck.splice(0, 2)
      initialGameState.players[startingPlayer].hand.push(...drawnCards)
      initialGameState.currentPlayer = (startingPlayer + 1) % playerCount
      setLastAction("Starting card is +2 - first player draws 2 cards!")
    }

    setGameState(initialGameState)
    setGameOver(false)
    setWinner(null)
    setGameMessage(
      `Round ${roundNumber} started! ${initialGameState.players[initialGameState.currentPlayer].name}'s turn.`,
    )
    setDrawnCardThisTurn(null)
    setCanPlayDrawnCard(false)

    // Initialize scores if first game
    if (scores.length === 0) {
      setScores(new Array(playerCount).fill(0))
    }
  }

  const playCard = useCallback(
    (card: UnoCard, colorChoice?: CardColor) => {
      if (!gameState || gameOver) return

      const currentPlayerData = gameState.players[gameState.currentPlayer]
      const topCard = gameState.discardPile[gameState.discardPile.length - 1]

      if (!canPlayCard(card, topCard, gameState.currentColor)) {
        setGameMessage("❌ That card cannot be played! Try a different card.")
        return
      }

      // Check Wild Draw Four validity
      if (card.type === "wild-draw4" && !isValidWildDrawFour(currentPlayerData.hand, gameState.currentColor)) {
        setGameMessage("❌ Wild Draw Four can only be played when you have no cards matching the current color!")
        return
      }

      // Remove card from player's hand
      const newHand = currentPlayerData.hand.filter(
        (c) => !(c.color === card.color && c.type === card.type && c.value === card.value),
      )

      const newPlayers = [...gameState.players]
      newPlayers[gameState.currentPlayer] = {
        ...currentPlayerData,
        hand: newHand,
        hasCalledUno: newHand.length === 1 ? true : false, // Automatically call UNO when down to 1 card
      }

      const newGameState: GameState = {
        ...gameState,
        players: newPlayers,
        discardPile: [...gameState.discardPile, card],
        currentColor: card.color === "wild" ? colorChoice || "red" : card.color,
        drawCount: 0,
      }

      // Handle special cards
      let skipNext = false
      let reverseDirection = false
      let drawCards = 0
      let actionMessage = ""

      switch (card.type) {
        case "skip":
          skipNext = true
          actionMessage = `${currentPlayerData.name} played SKIP! Next player loses their turn.`
          break
        case "reverse":
          reverseDirection = true
          actionMessage = `${currentPlayerData.name} played REVERSE! Direction changed.`
          break
        case "draw2":
          drawCards = 2
          skipNext = true
          actionMessage = `${currentPlayerData.name} played +2! Next player draws 2 cards.`
          break
        case "wild-draw4":
          drawCards = 4
          skipNext = true
          actionMessage = `${currentPlayerData.name} played Wild +4! Next player draws 4 cards.`
          break
        case "wild":
          actionMessage = `${currentPlayerData.name} played Wild card! Color changed to ${colorChoice || "red"}.`
          break
        case "number":
          actionMessage = `${currentPlayerData.name} played ${card.color} ${card.value}.`
          break
      }

      setLastAction(actionMessage)

      if (reverseDirection) {
        newGameState.direction *= -1
      }

      // Check for winner
      if (newHand.length === 0) {
        // Calculate round scores
        const roundScores = [...scores]
        let totalPoints = 0

        newGameState.players.forEach((player, index) => {
          if (index !== gameState.currentPlayer) {
            const playerScore = calculateScore(player.hand)
            totalPoints += playerScore
          }
        })

        roundScores[gameState.currentPlayer] += totalPoints
        setScores(roundScores)

        // Check if game is completely over (reached target score)
        if (roundScores[gameState.currentPlayer] >= targetScore) {
          setWinner(currentPlayerData)
          setGameOver(true)
          setGameMessage(
            `🎉 ${currentPlayerData.name} wins the entire game with ${roundScores[gameState.currentPlayer]} points!`,
          )
        } else {
          setGameMessage(`🎉 ${currentPlayerData.name} wins Round ${roundNumber}! (+${totalPoints} points)`)
          setTimeout(() => {
            setRoundNumber(roundNumber + 1)
            initializeGame()
          }, 3000)
        }

        setGameState(newGameState)
        return
      }

      // Announce UNO when player has 1 card
      if (newHand.length === 1) {
        setGameMessage(`🎯 ${currentPlayerData.name} called UNO! Only 1 card left!`)
      }

      // Move to next player
      let nextPlayer = gameState.currentPlayer + newGameState.direction
      if (nextPlayer >= playerCount) nextPlayer = 0
      if (nextPlayer < 0) nextPlayer = playerCount - 1

      // Handle Wild Draw Four challenge opportunity
      if (card.type === "wild-draw4" && !newGameState.players[nextPlayer].isAI) {
        setChallengeContext({
          challengerId: nextPlayer,
          playerId: gameState.currentPlayer,
          card: card,
        })
        setShowChallenge(true)
      }

      // Handle draw cards
      if (drawCards > 0) {
        const nextPlayerData = newPlayers[nextPlayer]

        if (newGameState.deck.length < drawCards) {
          // Reshuffle deck if needed
          const newDeck = shuffleDeck([...newGameState.discardPile.slice(0, -1)])
          newGameState.deck = newDeck
          newGameState.discardPile = [newGameState.discardPile[newGameState.discardPile.length - 1]]
        }

        const drawnCards = newGameState.deck.splice(0, drawCards)
        newPlayers[nextPlayer] = {
          ...nextPlayerData,
          hand: [...nextPlayerData.hand, ...drawnCards],
        }
      }

      // Skip next player if needed
      if (skipNext) {
        nextPlayer = nextPlayer + newGameState.direction
        if (nextPlayer >= playerCount) nextPlayer = 0
        if (nextPlayer < 0) nextPlayer = playerCount - 1
      }

      newGameState.currentPlayer = nextPlayer
      newGameState.players = newPlayers

      setGameState(newGameState)
      setSelectedCard(null)
      setGameMessage(`It's ${newGameState.players[nextPlayer].name}'s turn.`)
      setDrawnCardThisTurn(null)
      setCanPlayDrawnCard(false)
    },
    [gameState, gameOver, playerCount, scores, roundNumber, targetScore],
  )

  const drawCard = useCallback(() => {
    if (!gameState || gameOver) return

    const currentPlayerData = gameState.players[gameState.currentPlayer]

    // Check if deck needs reshuffling
    if (gameState.deck.length === 0) {
      if (gameState.discardPile.length <= 1) {
        setGameMessage("❌ No more cards to draw!")
        return
      }

      // Reshuffle discard pile
      const newDeck = shuffleDeck([...gameState.discardPile.slice(0, -1)])
      const drawnCard = newDeck.pop()!

      const newPlayers = [...gameState.players]
      newPlayers[gameState.currentPlayer] = {
        ...currentPlayerData,
        hand: [...currentPlayerData.hand, drawnCard],
      }

      const newGameState = {
        ...gameState,
        players: newPlayers,
        deck: newDeck,
        discardPile: [gameState.discardPile[gameState.discardPile.length - 1]],
      }

      setGameState(newGameState)
      setDrawnCardThisTurn(drawnCard)

      const topCard = gameState.discardPile[gameState.discardPile.length - 1]
      const canPlay = canPlayCard(drawnCard, topCard, gameState.currentColor)
      setCanPlayDrawnCard(canPlay)

      setGameMessage(
        `${currentPlayerData.name} drew from reshuffled deck. ${canPlay ? "The drawn card can be played immediately!" : "The drawn card cannot be played."}`,
      )
    } else {
      const drawnCard = gameState.deck.pop()!

      const newPlayers = [...gameState.players]
      newPlayers[gameState.currentPlayer] = {
        ...currentPlayerData,
        hand: [...currentPlayerData.hand, drawnCard],
      }

      const newGameState = {
        ...gameState,
        players: newPlayers,
        deck: [...gameState.deck],
      }

      setGameState(newGameState)
      setDrawnCardThisTurn(drawnCard)

      const topCard = gameState.discardPile[gameState.discardPile.length - 1]
      const canPlay = canPlayCard(drawnCard, topCard, gameState.currentColor)
      setCanPlayDrawnCard(canPlay)

      if (canPlay) {
        setGameMessage(
          `${currentPlayerData.name} drew a card that can be played! You may play it or pass your turn.`,
        )
      } else {
        // If drawn card can't be played, automatically pass turn
        setTimeout(() => {
          passTurn()
        }, 1000)
        setGameMessage(
          `${currentPlayerData.name} drew a card that cannot be played. Turn passes automatically.`,
        )
      }
    }
  }, [gameState, gameOver])

  const passTurn = useCallback(() => {
    if (!gameState || gameOver) return

    let nextPlayer = gameState.currentPlayer + gameState.direction
    if (nextPlayer >= playerCount) nextPlayer = 0
    if (nextPlayer < 0) nextPlayer = playerCount - 1

    setGameState({
      ...gameState,
      currentPlayer: nextPlayer,
    })

    setGameMessage(`${gameState.players[gameState.currentPlayer].name} passed their turn.`)
    setDrawnCardThisTurn(null)
    setCanPlayDrawnCard(false)
  }, [gameState, gameOver, playerCount])

  const callUno = useCallback(
    (playerId: number) => {
      if (!gameState) return

      const newPlayers = [...gameState.players]
      newPlayers[playerId] = {
        ...newPlayers[playerId],
        hasCalledUno: true,
      }

      setGameState({
        ...gameState,
        players: newPlayers,
      })

      setGameMessage(`${newPlayers[playerId].name} called UNO! 🎯`)
    },
    [gameState],
  )

  const catchUnoViolation = useCallback(
    (caughtPlayerId: number, catcherPlayerId: number) => {
      if (!gameState) return

      const caughtPlayer = gameState.players[caughtPlayerId]

      if (caughtPlayer.hand.length === 1 && !caughtPlayer.hasCalledUno) {
        // Add penalty cards
        if (gameState.deck.length < 2) {
          // Reshuffle if needed
          const newDeck = shuffleDeck([...gameState.discardPile.slice(0, -1)])
          gameState.deck = newDeck
          gameState.discardPile = [gameState.discardPile[gameState.discardPile.length - 1]]
        }

        const penaltyCards = gameState.deck.splice(0, 2)
        const newPlayers = [...gameState.players]
        newPlayers[caughtPlayerId] = {
          ...caughtPlayer,
          hand: [...caughtPlayer.hand, ...penaltyCards],
          hasCalledUno: false,
        }

        setGameState({
          ...gameState,
          players: newPlayers,
        })

        setGameMessage(
          `${gameState.players[catcherPlayerId].name} caught ${caughtPlayer.name}! ${caughtPlayer.name} draws 2 penalty cards.`,
        )
      }
    },
    [gameState],
  )

  const handleChallenge = useCallback(
    (challenge: boolean) => {
      if (!challengeContext || !gameState) return

      const { challengerId, playerId, card } = challengeContext
      const playerWhoPlayed = gameState.players[playerId]

      // Check if the Wild Draw Four was played legally
      const wasLegal = isValidWildDrawFour(playerWhoPlayed.hand, gameState.currentColor)

      if (challenge) {
        if (wasLegal) {
          // Challenge failed - challenger draws 6 cards (4 + 2 penalty)
          if (gameState.deck.length < 6) {
            const newDeck = shuffleDeck([...gameState.discardPile.slice(0, -1)])
            gameState.deck = newDeck
            gameState.discardPile = [gameState.discardPile[gameState.discardPile.length - 1]]
          }

          const penaltyCards = gameState.deck.splice(0, 6)
          const newPlayers = [...gameState.players]
          newPlayers[challengerId] = {
            ...newPlayers[challengerId],
            hand: [...newPlayers[challengerId].hand, ...penaltyCards],
          }

          setGameState({
            ...gameState,
            players: newPlayers,
          })

          setGameMessage(`Challenge failed! ${gameState.players[challengerId].name} draws 6 cards (4 + 2 penalty).`)
        } else {
          // Challenge succeeded - original player draws 4 cards instead
          if (gameState.deck.length < 4) {
            const newDeck = shuffleDeck([...gameState.discardPile.slice(0, -1)])
            gameState.deck = newDeck
            gameState.discardPile = [gameState.discardPile[gameState.discardPile.length - 1]]
          }

          const penaltyCards = gameState.deck.splice(0, 4)
          const newPlayers = [...gameState.players]
          newPlayers[playerId] = {
            ...newPlayers[playerId],
            hand: [...newPlayers[playerId].hand, ...penaltyCards],
          }

          setGameState({
            ...gameState,
            players: newPlayers,
          })

          setGameMessage(`Challenge succeeded! ${playerWhoPlayed.name} played Wild +4 illegally and draws 4 cards.`)
        }
      } else {
        // No challenge - proceed normally (challenger draws 4)
        if (gameState.deck.length < 4) {
          const newDeck = shuffleDeck([...gameState.discardPile.slice(0, -1)])
          gameState.deck = newDeck
          gameState.discardPile = [gameState.discardPile[gameState.discardPile.length - 1]]
        }

        const drawnCards = gameState.deck.splice(0, 4)
        const newPlayers = [...gameState.players]
        newPlayers[challengerId] = {
          ...newPlayers[challengerId],
          hand: [...newPlayers[challengerId].hand, ...drawnCards],
        }

        setGameState({
          ...gameState,
          players: newPlayers,
        })

        setGameMessage(`${gameState.players[challengerId].name} draws 4 cards from Wild +4.`)
      }

      setShowChallenge(false)
      setChallengeContext(null)
    },
    [challengeContext, gameState],
  )

  // AI turn handling
  useEffect(() => {
    if (!gameState || gameOver) return

    const currentPlayerData = gameState.players[gameState.currentPlayer]

    if (currentPlayerData.isAI) {
      const timer = setTimeout(() => {
        playAITurn(gameState, playCard, drawCard, passTurn, callUno)
      }, 1500)

      return () => clearTimeout(timer)
    }
  }, [gameState?.currentPlayer, gameState, gameOver, playCard, drawCard, passTurn, callUno])

  const handleCardClick = (card: UnoCard) => {
    if (!gameState || gameOver) return

    const currentPlayerData = gameState.players[gameState.currentPlayer]
    if (currentPlayerData.isAI) return

    const topCard = gameState.discardPile[gameState.discardPile.length - 1]

    if (!canPlayCard(card, topCard, gameState.currentColor)) {
      setGameMessage("❌ That card cannot be played! It must match the color, number, or symbol.")
      return
    }

    if (card.color === "wild") {
      setSelectedCard(card)
      setShowColorPicker(true)
    } else {
      playCard(card)
    }
  }

  const handleColorChoice = (color: CardColor) => {
    if (selectedCard) {
      playCard(selectedCard, color)
    }
    setShowColorPicker(false)
    setSelectedCard(null)
  }

  const playDrawnCard = () => {
    if (drawnCardThisTurn && canPlayDrawnCard) {
      handleCardClick(drawnCardThisTurn)
    }
  }

  if (!gameState) {
    return (
      <div className="min-h-screen bg-green-800 flex items-center justify-center">
        <div className="text-white text-xl">Setting up your UNO game...</div>
      </div>
    )
  }

  const currentPlayerData = gameState.players[gameState.currentPlayer]
  const topCard = gameState.discardPile[gameState.discardPile.length - 1]

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-800 to-green-600 p-2 md:p-4">
      {/* Header */}
      <div className="flex justify-between items-center mb-4 bg-white/10 rounded-lg p-3">
        <Button variant="ghost" onClick={onExit} className="text-white hover:bg-white/20">
          <ArrowLeft className="mr-2" size={20} />
          Exit
        </Button>

        <div className="text-white text-center">
          <h1 className="text-xl md:text-2xl font-bold">UNO Game - Round {roundNumber}</h1>
          <TurnIndicator currentPlayer={currentPlayerData} />
        </div>

        <div className="flex gap-2">
          <Button variant="ghost" onClick={() => setShowInstructions(true)} className="text-white hover:bg-white/20">
            <HelpCircle size={20} />
          </Button>
          <Button
            variant="ghost"
            onClick={() => setSoundEnabled(!soundEnabled)}
            className="text-white hover:bg-white/20"
          >
            {soundEnabled ? <Volume2 size={20} /> : <VolumeX size={20} />}
          </Button>
        </div>
      </div>

      {/* Score Board */}
      <ScoreBoard players={gameState.players} scores={scores} targetScore={targetScore} />

      {/* Game Message */}
      {gameMessage && (
        <div className="bg-white/90 rounded-lg p-3 mb-4 text-center max-w-4xl mx-auto">
          <p className="font-medium text-gray-800">{gameMessage}</p>
          {lastAction && <p className="text-sm text-gray-600 mt-1">{lastAction}</p>}
        </div>
      )}

      {/* Game Status */}
      <GameStatus gameState={gameState} />

      {/* Game Board */}
      <div className="flex flex-col items-center space-y-4 md:space-y-6">
        {/* Other Players */}
        <div className="w-full max-w-6xl">
          {gameState.players.map((player, index) => {
            if (index === 0) return null // Skip human player

            return (
              <div key={player.id} className="mb-3">
                <div className="flex items-center justify-between bg-white/10 rounded-lg p-3">
                  <div className="text-white">
                    <span className="font-semibold">{player.name}</span>
                    <span className="ml-2 text-sm opacity-80">{player.hand.length} cards</span>
                    {gameState.currentPlayer === index && (
                      <span className="ml-2 bg-yellow-400 text-black px-2 py-1 rounded text-xs font-bold">PLAYING</span>
                    )}
                    {player.hand.length === 1 && !player.hasCalledUno && (
                      <div className="ml-2 inline-flex items-center">
                        <span className="text-red-300 text-sm animate-pulse mr-2">⚠️ Forgot UNO!</span>
                        <Button
                          size="sm"
                          variant="outline"
                          className="bg-red-500 text-white border-red-600 hover:bg-red-600 text-xs"
                          onClick={() => catchUnoViolation(index, 0)}
                        >
                          Catch!
                        </Button>
                      </div>
                    )}
                    {player.hand.length === 1 && player.hasCalledUno && (
                      <span className="ml-2 text-yellow-300 text-sm">🎯 UNO!</span>
                    )}
                  </div>

                  <div className="flex space-x-1">
                    {player.hand.slice(0, Math.min(10, player.hand.length)).map((_, cardIndex) => (
                      <div
                        key={cardIndex}
                        className="w-6 md:w-8 h-8 md:h-12"
                        style={{ marginLeft: cardIndex > 0 ? "-12px" : "0" }}
                      >
                        <UnoCardComponent
                          card={{ color: "wild", type: "wild" }} // Dummy card for back image
                          showBack={true}
                          size="small"
                          isPlayable={false}
                        />
                      </div>
                    ))}
                    {player.hand.length > 10 && (
                      <div className="text-white text-sm ml-2">+{player.hand.length - 10}</div>
                    )}
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Center Area - Deck and Discard Pile */}
        <div className="flex items-center space-x-4 md:space-x-8">
          {/* Draw Pile */}
          <div className="text-center">
            <div className="relative cursor-pointer hover:scale-105 transition-transform" onClick={drawCard}>
              <UnoCardComponent
                card={{ color: "wild", type: "wild" }} // Dummy card for back image
                showBack={true}
                size="medium"
                isPlayable={true}
              />
              <div className="absolute inset-0 flex flex-col items-center justify-center bg-black/30 rounded-lg">
                <div className="text-white text-xs font-bold">DRAW</div>
                <div className="text-white text-xs mt-1">{gameState.deck.length}</div>
              </div>
            </div>
            <p className="text-white text-xs mt-2">Click to draw</p>
          </div>

          {/* Current Card Info */}
          <div className="text-center bg-white/10 rounded-lg p-3">
            <div className="text-white text-sm font-semibold mb-2">Match This Card</div>
            <UnoCardComponent card={topCard} size="large" />
            <div className="mt-2 text-white text-sm">
              <div>
                Color: <span className="font-bold capitalize">{gameState.currentColor}</span>
              </div>
              <div className="text-xs opacity-80 mt-1">Match color, number, or symbol</div>
            </div>
          </div>

          {/* Game Controls */}
          <div className="text-center space-y-2">
            {gameState.currentPlayer === 0 && !gameOver && (
              <>
                {drawnCardThisTurn && canPlayDrawnCard && (
                  <Button onClick={playDrawnCard} className="bg-green-600 hover:bg-green-700 text-white w-full">
                    Play Drawn Card
                  </Button>
                )}
                {drawnCardThisTurn && canPlayDrawnCard && (
                  <Button
                    onClick={passTurn}
                    variant="outline"
                    className="bg-white/20 text-white border-white/30 hover:bg-white/30 w-full"
                  >
                    Pass Turn
                  </Button>
                )}
                <Button
                  onClick={initializeGame}
                  variant="outline"
                  size="sm"
                  className="bg-white/20 text-white border-white/30 hover:bg-white/30 w-full"
                >
                  <RotateCcw size={16} className="mr-1" />
                  New Round
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Human Player Hand */}
        <PlayerHand
          player={gameState.players[0]}
          isCurrentPlayer={gameState.currentPlayer === 0}
          onCardClick={handleCardClick}
          onUnoCall={() => {}} // UNO is now automatic
          topCard={topCard}
          currentColor={gameState.currentColor}
          drawnCard={drawnCardThisTurn}
        />
      </div>

      {/* Color Picker Modal */}
      {showColorPicker && (
        <ColorPicker
          onColorSelect={handleColorChoice}
          onClose={() => {
            setShowColorPicker(false)
            setSelectedCard(null)
          }}
        />
      )}

      {/* Challenge Modal */}
      {showChallenge && challengeContext && (
        <ChallengeModal
          challengerName={gameState.players[challengeContext.challengerId].name}
          playerName={gameState.players[challengeContext.playerId].name}
          onChallenge={handleChallenge}
        />
      )}

      {/* Instructions Modal */}
      {showInstructions && <GameInstructions onClose={() => setShowInstructions(false)} />}

      {/* Game Over Modal */}
      {gameOver && winner && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="p-6 max-w-md w-full">
            <h2 className="text-2xl font-bold mb-4">Game Over!</h2>
            <p className="text-gray-600 mb-6">{winner.name} wins the game!</p>
            <Button onClick={onExit} className="w-full">
              Back to Menu
            </Button>
          </Card>
        </div>
      )}
    </div>
  )
}
