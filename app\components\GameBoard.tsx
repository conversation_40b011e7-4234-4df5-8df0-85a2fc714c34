"use client"

import { useState, useEffect, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { ArrowLeft, Volume2, VolumeX, HelpCircle, RotateCcw } from "lucide-react"
import type { GameState, Player, UnoCard, CardColor } from "../types/game"
import { createDeck, shuffleDeck, canPlayCard, isValidWildDrawFour, canChallengeWildDrawFour, calculateScore } from "../utils/gameLogic"
import { playAITurn } from "../utils/aiLogic"
import { preloadCardImages } from "../utils/cardImages"
import UnoCardComponent from "./UnoCard"
import PlayerHand from "./PlayerHand"
import GameStatus from "./GameStatus"
import ColorPicker from "./ColorPicker"
import GameInstructions from "./GameInstructions"
import StackingModal from "./StackingModal"
import TurnIndicator from "./TurnIndicator"
import ChallengeModal from "./ChallengeModal"
import ScoreBoard from "./ScoreBoard"

interface GameBoardProps {
  gameMode: "single" | "online"
  playerCount?: number
  roomData?: any
  onExit: () => void
}

export default function GameBoard({ gameMode, playerCount = 2, roomData, onExit }: GameBoardProps) {
  const [gameState, setGameState] = useState<GameState | null>(null)
  const [selectedCard, setSelectedCard] = useState<UnoCard | null>(null)
  const [showColorPicker, setShowColorPicker] = useState(false)
  const [soundEnabled, setSoundEnabled] = useState(true)
  const [gameOver, setGameOver] = useState(false)
  const [winner, setWinner] = useState<Player | null>(null)
  const [showInstructions, setShowInstructions] = useState(false)
  const [gameMessage, setGameMessage] = useState<string>("")
  const [lastAction, setLastAction] = useState<string>("")
  const [showChallenge, setShowChallenge] = useState(false)
  const [challengeContext, setChallengeContext] = useState<{
    challengerId: number
    playerId: number
    card: UnoCard
  } | null>(null)
  const [showStacking, setShowStacking] = useState(false)
  const [stackingContext, setStackingContext] = useState<{
    playerId: number
    stackType: "draw2" | "draw4"
    currentPenalty: number
  } | null>(null)

  const [scores, setScores] = useState<number[]>([])
  const [roundNumber, setRoundNumber] = useState(1)
  const [targetScore] = useState(500)
  const [drawnCardThisTurn, setDrawnCardThisTurn] = useState<UnoCard | null>(null)
  const [canPlayDrawnCard, setCanPlayDrawnCard] = useState(false)

  // Single player game logic (existing code)
  useEffect(() => {
    initializeGame()
  }, [playerCount])

  // If online mode, use the online game board
  useEffect(() => {
    if (gameMode === "online" && roomData) {
      setGameMessage("Online Game Coming Soon!")
      setGameOver(true)
    }
  }, [gameMode, roomData])

  // Preload card images for better performance
  useEffect(() => {
    preloadCardImages()
  }, [])

  const initializeGame = () => {
    const deck = shuffleDeck(createDeck())
    const players: Player[] = []

    // Create players
    for (let i = 0; i < playerCount; i++) {
      players.push({
        id: i,
        name: i === 0 ? "You" : `AI Player ${i}`,
        hand: deck.splice(0, 7),
        isAI: i > 0,
        hasCalledUno: false,
      })
    }

    // Determine starting player (random, as per UNO rules)
    const startingPlayer = Math.floor(Math.random() * playerCount)

    let topCard = deck.pop()!

    // Handle special starting cards
    while (topCard.color === "wild") {
      deck.push(topCard)
      deck.splice(Math.floor(Math.random() * deck.length), 0, topCard)
      topCard = deck.pop()!
    }

    const initialGameState: GameState = {
      players,
      deck,
      discardPile: [topCard],
      currentPlayer: startingPlayer,
      direction: 1,
      currentColor: topCard.color === "wild" ? "red" : topCard.color,
      drawCount: 0,
      gameStarted: true,
      stackingType: "none",
    }

    // Handle starting action cards
    if (topCard.type === "skip") {
      initialGameState.currentPlayer = (startingPlayer + 1) % playerCount
      setLastAction("Starting card is SKIP - first player's turn is skipped!")
    } else if (topCard.type === "reverse") {
      initialGameState.direction = -1
      initialGameState.currentPlayer = startingPlayer === 0 ? playerCount - 1 : startingPlayer - 1
      setLastAction("Starting card is REVERSE - direction reversed!")
    } else if (topCard.type === "draw2") {
      const drawnCards = initialGameState.deck.splice(0, 2)
      initialGameState.players[startingPlayer].hand.push(...drawnCards)
      initialGameState.currentPlayer = (startingPlayer + 1) % playerCount
      setLastAction("Starting card is +2 - first player draws 2 cards!")
    }

    setGameState(initialGameState)
    setGameOver(false)
    setWinner(null)
    setGameMessage(
      `Round ${roundNumber} started! ${initialGameState.players[initialGameState.currentPlayer].name}'s turn.`,
    )
    setDrawnCardThisTurn(null)
    setCanPlayDrawnCard(false)

    // Initialize scores if first game
    if (scores.length === 0) {
      setScores(new Array(playerCount).fill(0))
    }
  }

  const playCard = useCallback(
    (card: UnoCard, colorChoice?: CardColor) => {
      if (!gameState || gameOver) return

      const currentPlayerData = gameState.players[gameState.currentPlayer]
      const topCard = gameState.discardPile[gameState.discardPile.length - 1]

      if (!canPlayCard(card, topCard, gameState.currentColor, gameState.stackingType)) {
        if (gameState.stackingType === "draw2") {
          setGameMessage("❌ You must play a Draw 2 card to stack, or draw the cards!")
        } else if (gameState.stackingType === "draw4") {
          setGameMessage("❌ You must play a Wild Draw 4 card to stack, or draw the cards!")
        } else {
          setGameMessage("❌ That card cannot be played! Try a different card.")
        }
        return
      }

      // Check Wild Draw Four validity
      if (card.type === "wild-draw4" && !isValidWildDrawFour(currentPlayerData.hand, gameState.currentColor)) {
        setGameMessage("❌ Wild Draw Four can only be played when you have no cards matching the current color!")
        return
      }

      // Remove card from player's hand
      const newHand = currentPlayerData.hand.filter(
        (c) => !(c.color === card.color && c.type === card.type && c.value === card.value),
      )

      const newPlayers = [...gameState.players]
      newPlayers[gameState.currentPlayer] = {
        ...currentPlayerData,
        hand: newHand,
        hasCalledUno: newHand.length === 1 ? currentPlayerData.hasCalledUno : false, // Keep UNO status if down to 1 card
      }

      const newGameState: GameState = {
        ...gameState,
        players: newPlayers,
        discardPile: [...gameState.discardPile, card],
        currentColor: card.color === "wild" ? colorChoice || "red" : card.color,
        drawCount: gameState.drawCount, // Keep existing draw count for stacking
        stackingType: gameState.stackingType, // Keep stacking state initially
      }

      // Handle special cards
      let skipNext = false
      let reverseDirection = false
      let drawCards = 0
      let actionMessage = ""

      switch (card.type) {
        case "skip":
          skipNext = true
          newGameState.drawCount = 0
          newGameState.stackingType = "none"
          actionMessage = `${currentPlayerData.name} played SKIP! Next player loses their turn.`
          break
        case "reverse":
          reverseDirection = true
          newGameState.drawCount = 0
          newGameState.stackingType = "none"
          actionMessage = `${currentPlayerData.name} played REVERSE! Direction changed.`
          break
        case "draw2":
          if (gameState.stackingType === "draw2") {
            // Stacking Draw 2 cards
            newGameState.drawCount += 2
            newGameState.stackingType = "draw2"
            actionMessage = `${currentPlayerData.name} stacked +2! Total penalty is now +${newGameState.drawCount}. Next player must draw or stack.`
          } else {
            // First Draw 2 card
            newGameState.drawCount = 2
            newGameState.stackingType = "draw2"
            actionMessage = `${currentPlayerData.name} played +2! Next player must draw 2 cards or stack another +2.`
          }
          break
        case "wild-draw4":
          if (gameState.stackingType === "draw4") {
            // Stacking Wild Draw 4 cards
            newGameState.drawCount += 4
            newGameState.stackingType = "draw4"
            actionMessage = `${currentPlayerData.name} stacked Wild +4! Total penalty is now +${newGameState.drawCount}. Next player must draw or stack.`
          } else {
            // First Wild Draw 4 card
            newGameState.drawCount = 4
            newGameState.stackingType = "draw4"
            actionMessage = `${currentPlayerData.name} played Wild +4! Next player must draw 4 cards or stack another Wild +4.`
          }
          break
        case "wild":
          newGameState.drawCount = 0
          newGameState.stackingType = "none"
          actionMessage = `${currentPlayerData.name} played Wild card! Color changed to ${colorChoice || "red"}.`
          break
        case "number":
          newGameState.drawCount = 0
          newGameState.stackingType = "none"
          actionMessage = `${currentPlayerData.name} played ${card.color} ${card.value}.`
          break
      }

      setLastAction(actionMessage)

      if (reverseDirection) {
        newGameState.direction *= -1
      }

      // Check for winner
      if (newHand.length === 0) {
        // Calculate round scores
        const roundScores = [...scores]
        let totalPoints = 0

        newGameState.players.forEach((player, index) => {
          if (index !== gameState.currentPlayer) {
            const playerScore = calculateScore(player.hand)
            totalPoints += playerScore
          }
        })

        roundScores[gameState.currentPlayer] += totalPoints
        setScores(roundScores)

        // Check if game is completely over (reached target score)
        if (roundScores[gameState.currentPlayer] >= targetScore) {
          setWinner(currentPlayerData)
          setGameOver(true)
          setGameMessage(
            `🎉 ${currentPlayerData.name} wins the entire game with ${roundScores[gameState.currentPlayer]} points!`,
          )
        } else {
          setGameMessage(`🎉 ${currentPlayerData.name} wins Round ${roundNumber}! (+${totalPoints} points)`)
          setTimeout(() => {
            setRoundNumber(roundNumber + 1)
            initializeGame()
          }, 3000)
        }

        setGameState(newGameState)
        return
      }

      // Check UNO status when player has 1 card
      if (newHand.length === 1) {
        if (newPlayers[gameState.currentPlayer].hasCalledUno) {
          setGameMessage(`🎯 ${currentPlayerData.name} has UNO! Only 1 card left!`)
        } else {
          setGameMessage(`⚠️ ${currentPlayerData.name} has 1 card but hasn't called UNO! Other players can catch them!`)
        }
      }

      // Move to next player
      let nextPlayer = gameState.currentPlayer + newGameState.direction
      if (nextPlayer >= playerCount) nextPlayer = 0
      if (nextPlayer < 0) nextPlayer = playerCount - 1

      // Handle Wild Draw Four - challenge opportunity comes first, then stacking
      if (card.type === "wild-draw4") {
        // Set up the game state first
        setGameState(newGameState)
        setSelectedCard(null)
        setDrawnCardThisTurn(null)
        setCanPlayDrawnCard(false)

        if (!newGameState.players[nextPlayer].isAI) {
          // Human player gets challenge option first
          setChallengeContext({
            challengerId: nextPlayer,
            playerId: gameState.currentPlayer,
            card: card,
          })
          setShowChallenge(true)
          return // Challenge modal will handle the rest
        } else {
          // AI player automatically decides whether to challenge (30% chance if challenge would succeed)
          const wouldSucceed = canChallengeWildDrawFour(currentPlayerData.hand, gameState.currentColor)
          const shouldChallenge = wouldSucceed && Math.random() < 0.3

          if (shouldChallenge) {
            // AI challenges
            setChallengeContext({
              challengerId: nextPlayer,
              playerId: gameState.currentPlayer,
              card: card,
            })
            setTimeout(() => {
              handleChallenge(true)
            }, 1500)
            setGameMessage(`${newGameState.players[nextPlayer].name} is considering a challenge...`)
            return
          } else {
            // AI doesn't challenge, proceed with normal Wild Draw 4 flow
            // This will be handled by the normal game flow below
          }
        }
      }

      // Handle stacking for Draw 2 cards (Wild Draw 4 is handled by challenge system)
      if (card.type === "draw2" && gameState.stackingType === "none") {
        // First Draw 2 card - offer stacking to next player
        const nextPlayerData = newGameState.players[nextPlayer]
        const hasDrawTwo = nextPlayerData.hand.some(card => card.type === "draw2")

        if (!nextPlayerData.isAI && hasDrawTwo) {
          // Human player with Draw 2 - show stacking modal
          setStackingContext({
            playerId: nextPlayer,
            stackType: "draw2",
            currentPenalty: newGameState.drawCount,
          })
          setShowStacking(true)
          return
        } else if (nextPlayerData.isAI && hasDrawTwo && Math.random() < 0.8) {
          // AI player decides to stack (80% chance if they have the card)
          const drawTwo = nextPlayerData.hand.find(card => card.type === "draw2")!
          setTimeout(() => {
            // Temporarily set current player to AI for stacking
            const tempGameState = { ...newGameState, currentPlayer: nextPlayer }
            setGameState(tempGameState)
            setTimeout(() => {
              playCard(drawTwo)
            }, 500)
          }, 1000)
          return
        }
        // If no stacking, continue with normal flow (player will be forced to draw)
      }

      // Skip next player if needed (for Skip card or after drawing cards)
      if (skipNext) {
        nextPlayer = nextPlayer + newGameState.direction
        if (nextPlayer >= playerCount) nextPlayer = 0
        if (nextPlayer < 0) nextPlayer = playerCount - 1
      }

      newGameState.currentPlayer = nextPlayer
      newGameState.players = newPlayers

      setGameState(newGameState)
      setSelectedCard(null)

      // Set appropriate message
      if (actionMessage) {
        setGameMessage(actionMessage + ` It's ${newGameState.players[nextPlayer].name}'s turn.`)
      } else {
        setGameMessage(`It's ${newGameState.players[nextPlayer].name}'s turn.`)
      }

      setDrawnCardThisTurn(null)
      setCanPlayDrawnCard(false)
    },
    [gameState, gameOver, playerCount, scores, roundNumber, targetScore],
  )

  const drawCard = useCallback(() => {
    if (!gameState || gameOver) return

    const currentPlayerData = gameState.players[gameState.currentPlayer]

    // Check if we're in a stacking situation
    if (gameState.stackingType !== "none" && gameState.drawCount > 0) {
      // Player must draw the stacked penalty cards
      const cardsToDraw = gameState.drawCount

      if (gameState.deck.length < cardsToDraw) {
        // Reshuffle discard pile into deck
        const newDeck = shuffleDeck([...gameState.discardPile.slice(0, -1)])
        gameState.deck = newDeck
        gameState.discardPile = [gameState.discardPile[gameState.discardPile.length - 1]]
      }

      const drawnCards = gameState.deck.splice(0, cardsToDraw)
      const newPlayers = [...gameState.players]
      newPlayers[gameState.currentPlayer] = {
        ...currentPlayerData,
        hand: [...currentPlayerData.hand, ...drawnCards],
      }

      const newGameState: GameState = {
        ...gameState,
        players: newPlayers,
        drawCount: 0,
        stackingType: "none",
      }

      setGameState(newGameState)
      setGameMessage(`${currentPlayerData.name} drew ${cardsToDraw} penalty cards and loses their turn.`)

      // Skip turn after drawing penalty cards
      setTimeout(() => {
        passTurn()
      }, 1000)
      return
    }

    // Check if deck needs reshuffling
    if (gameState.deck.length === 0) {
      if (gameState.discardPile.length <= 1) {
        setGameMessage("❌ No more cards to draw!")
        return
      }

      // Reshuffle discard pile
      const newDeck = shuffleDeck([...gameState.discardPile.slice(0, -1)])
      const drawnCard = newDeck.pop()!

      const newPlayers = [...gameState.players]
      newPlayers[gameState.currentPlayer] = {
        ...currentPlayerData,
        hand: [...currentPlayerData.hand, drawnCard],
      }

      const newGameState = {
        ...gameState,
        players: newPlayers,
        deck: newDeck,
        discardPile: [gameState.discardPile[gameState.discardPile.length - 1]],
      }

      setGameState(newGameState)
      setDrawnCardThisTurn(drawnCard)

      const topCard = gameState.discardPile[gameState.discardPile.length - 1]
      const canPlay = canPlayCard(drawnCard, topCard, gameState.currentColor)
      setCanPlayDrawnCard(canPlay)

      setGameMessage(
        `${currentPlayerData.name} drew from reshuffled deck. ${canPlay ? "The drawn card can be played immediately!" : "The drawn card cannot be played."}`,
      )
    } else {
      const drawnCard = gameState.deck.pop()!

      const newPlayers = [...gameState.players]
      newPlayers[gameState.currentPlayer] = {
        ...currentPlayerData,
        hand: [...currentPlayerData.hand, drawnCard],
      }

      const newGameState = {
        ...gameState,
        players: newPlayers,
        deck: [...gameState.deck],
      }

      setGameState(newGameState)
      setDrawnCardThisTurn(drawnCard)

      const topCard = gameState.discardPile[gameState.discardPile.length - 1]
      const canPlay = canPlayCard(drawnCard, topCard, gameState.currentColor)
      setCanPlayDrawnCard(canPlay)

      if (canPlay) {
        setGameMessage(
          `${currentPlayerData.name} drew a card that can be played! You may play it or pass your turn.`,
        )
      } else {
        // If drawn card can't be played, automatically pass turn
        setTimeout(() => {
          passTurn()
        }, 1000)
        setGameMessage(
          `${currentPlayerData.name} drew a card that cannot be played. Turn passes automatically.`,
        )
      }
    }
  }, [gameState, gameOver])

  const passTurn = useCallback(() => {
    if (!gameState || gameOver) return

    let nextPlayer = gameState.currentPlayer + gameState.direction
    if (nextPlayer >= playerCount) nextPlayer = 0
    if (nextPlayer < 0) nextPlayer = playerCount - 1

    setGameState({
      ...gameState,
      currentPlayer: nextPlayer,
    })

    setGameMessage(`${gameState.players[gameState.currentPlayer].name} passed their turn.`)
    setDrawnCardThisTurn(null)
    setCanPlayDrawnCard(false)
  }, [gameState, gameOver, playerCount])

  const callUno = useCallback(
    (playerId: number) => {
      if (!gameState) return

      const newPlayers = [...gameState.players]
      newPlayers[playerId] = {
        ...newPlayers[playerId],
        hasCalledUno: true,
      }

      setGameState({
        ...gameState,
        players: newPlayers,
      })

      setGameMessage(`${newPlayers[playerId].name} called UNO! 🎯`)
    },
    [gameState],
  )

  const catchUnoViolation = useCallback(
    (caughtPlayerId: number, catcherPlayerId: number) => {
      if (!gameState) return

      const caughtPlayer = gameState.players[caughtPlayerId]

      if (caughtPlayer.hand.length === 1 && !caughtPlayer.hasCalledUno) {
        // Add penalty cards
        if (gameState.deck.length < 2) {
          // Reshuffle if needed
          const newDeck = shuffleDeck([...gameState.discardPile.slice(0, -1)])
          gameState.deck = newDeck
          gameState.discardPile = [gameState.discardPile[gameState.discardPile.length - 1]]
        }

        const penaltyCards = gameState.deck.splice(0, 2)
        const newPlayers = [...gameState.players]
        newPlayers[caughtPlayerId] = {
          ...caughtPlayer,
          hand: [...caughtPlayer.hand, ...penaltyCards],
          hasCalledUno: false,
        }

        setGameState({
          ...gameState,
          players: newPlayers,
        })

        setGameMessage(
          `${gameState.players[catcherPlayerId].name} caught ${caughtPlayer.name}! ${caughtPlayer.name} draws 2 penalty cards.`,
        )
      }
    },
    [gameState],
  )

  const handleChallenge = useCallback(
    (challenge: boolean) => {
      if (!challengeContext || !gameState) return

      const { challengerId, playerId, card } = challengeContext
      const playerWhoPlayed = gameState.players[playerId]

      // Check if the Wild Draw Four was played legally
      // Challenge succeeds if the player had cards matching the current color
      const challengeSucceeds = canChallengeWildDrawFour(playerWhoPlayed.hand, gameState.currentColor)

      let nextPlayer = challengerId
      const playerCount = gameState.players.length

      if (challenge) {
        if (challengeSucceeds) {
          // Challenge succeeded - original player draws 4 cards instead of challenger
          if (gameState.deck.length < 4) {
            const newDeck = shuffleDeck([...gameState.discardPile.slice(0, -1)])
            gameState.deck = newDeck
            gameState.discardPile = [gameState.discardPile[gameState.discardPile.length - 1]]
          }

          const penaltyCards = gameState.deck.splice(0, 4)
          const newPlayers = [...gameState.players]
          newPlayers[playerId] = {
            ...newPlayers[playerId],
            hand: [...newPlayers[playerId].hand, ...penaltyCards],
          }

          // Challenger doesn't lose their turn when challenge succeeds
          setGameState({
            ...gameState,
            players: newPlayers,
            currentPlayer: challengerId,
            drawCount: 0,
            stackingType: "none",
          })

          setGameMessage(`Challenge succeeded! ${playerWhoPlayed.name} had cards matching the current color and draws 4 cards. ${gameState.players[challengerId].name}'s turn.`)
        } else {
          // Challenge failed - challenger draws 6 cards (4 + 2 penalty) AND loses turn
          if (gameState.deck.length < 6) {
            const newDeck = shuffleDeck([...gameState.discardPile.slice(0, -1)])
            gameState.deck = newDeck
            gameState.discardPile = [gameState.discardPile[gameState.discardPile.length - 1]]
          }

          const penaltyCards = gameState.deck.splice(0, 6)
          const newPlayers = [...gameState.players]
          newPlayers[challengerId] = {
            ...newPlayers[challengerId],
            hand: [...newPlayers[challengerId].hand, ...penaltyCards],
          }

          // Skip challenger's turn
          nextPlayer = nextPlayer + gameState.direction
          if (nextPlayer >= playerCount) nextPlayer = 0
          if (nextPlayer < 0) nextPlayer = playerCount - 1

          setGameState({
            ...gameState,
            players: newPlayers,
            currentPlayer: nextPlayer,
            drawCount: 0,
            stackingType: "none",
          })

          setGameMessage(`Challenge failed! ${gameState.players[challengerId].name} draws 6 cards (4 + 2 penalty) and loses their turn.`)
        }
      } else {
        // No challenge - now check if player can stack or must draw
        const challengerPlayer = gameState.players[challengerId]
        const hasWildDraw4 = challengerPlayer.hand.some(card => card.type === "wild-draw4")

        if (!challengerPlayer.isAI && hasWildDraw4) {
          // Human player with Wild Draw 4 - show stacking modal
          setStackingContext({
            playerId: challengerId,
            stackType: "draw4",
            currentPenalty: gameState.stackingType === "draw4" ? gameState.drawCount + 4 : 4,
          })
          setShowStacking(true)
        } else if (challengerPlayer.isAI && hasWildDraw4 && Math.random() < 0.7) {
          // AI player decides to stack (70% chance if they have the card)
          const wildDraw4 = challengerPlayer.hand.find(card => card.type === "wild-draw4")!
          setTimeout(() => {
            playCard(wildDraw4, "red") // AI chooses red as default
          }, 1000)
        } else {
          // Player doesn't have Wild Draw 4 or chooses not to stack - draw penalty cards
          const penaltyCards = gameState.stackingType === "draw4" ? gameState.drawCount + 4 : 4

          if (gameState.deck.length < penaltyCards) {
            const newDeck = shuffleDeck([...gameState.discardPile.slice(0, -1)])
            gameState.deck = newDeck
            gameState.discardPile = [gameState.discardPile[gameState.discardPile.length - 1]]
          }

          const drawnCards = gameState.deck.splice(0, penaltyCards)
          const newPlayers = [...gameState.players]
          newPlayers[challengerId] = {
            ...newPlayers[challengerId],
            hand: [...newPlayers[challengerId].hand, ...drawnCards],
          }

          // Skip challenger's turn
          nextPlayer = nextPlayer + gameState.direction
          if (nextPlayer >= playerCount) nextPlayer = 0
          if (nextPlayer < 0) nextPlayer = playerCount - 1

          setGameState({
            ...gameState,
            players: newPlayers,
            currentPlayer: nextPlayer,
            drawCount: 0,
            stackingType: "none",
          })

          setGameMessage(`${gameState.players[challengerId].name} draws ${penaltyCards} cards from Wild +4 and loses their turn.`)
        }
      }

      setShowChallenge(false)
      setChallengeContext(null)
    },
    [challengeContext, gameState],
  )

  const handleStacking = useCallback(
    (stack: boolean) => {
      if (!stackingContext || !gameState) return

      const { playerId, stackType, currentPenalty } = stackingContext
      const player = gameState.players[playerId]

      if (stack) {
        // Player chooses to stack
        const stackCard = player.hand.find(card =>
          stackType === "draw2" ? card.type === "draw2" : card.type === "wild-draw4"
        )

        if (stackCard) {
          if (stackCard.type === "wild-draw4") {
            // For Wild Draw 4, show color picker
            setSelectedCard(stackCard)
            setShowColorPicker(true)
          } else {
            // For Draw 2, play directly
            playCard(stackCard)
          }
        }
      } else {
        // Player chooses to draw penalty cards
        if (gameState.deck.length < currentPenalty) {
          const newDeck = shuffleDeck([...gameState.discardPile.slice(0, -1)])
          gameState.deck = newDeck
          gameState.discardPile = [gameState.discardPile[gameState.discardPile.length - 1]]
        }

        const drawnCards = gameState.deck.splice(0, currentPenalty)
        const newPlayers = [...gameState.players]
        newPlayers[playerId] = {
          ...newPlayers[playerId],
          hand: [...newPlayers[playerId].hand, ...drawnCards],
        }

        // Skip player's turn
        let nextPlayer = playerId + gameState.direction
        if (nextPlayer >= gameState.players.length) nextPlayer = 0
        if (nextPlayer < 0) nextPlayer = gameState.players.length - 1

        setGameState({
          ...gameState,
          players: newPlayers,
          currentPlayer: nextPlayer,
          drawCount: 0,
          stackingType: "none",
        })

        setGameMessage(`${player.name} draws ${currentPenalty} penalty cards and loses their turn.`)
      }

      setShowStacking(false)
      setStackingContext(null)
    },
    [stackingContext, gameState, playCard],
  )

  // AI turn handling
  useEffect(() => {
    if (!gameState || gameOver) return

    const currentPlayerData = gameState.players[gameState.currentPlayer]

    if (currentPlayerData.isAI) {
      const timer = setTimeout(() => {
        playAITurn(gameState, playCard, drawCard, passTurn, callUno)
      }, 1500)

      return () => clearTimeout(timer)
    }
  }, [gameState?.currentPlayer, gameState, gameOver, playCard, drawCard, passTurn, callUno])

  const handleCardClick = (card: UnoCard) => {
    if (!gameState || gameOver) return

    const currentPlayerData = gameState.players[gameState.currentPlayer]
    if (currentPlayerData.isAI) return

    const topCard = gameState.discardPile[gameState.discardPile.length - 1]

    if (!canPlayCard(card, topCard, gameState.currentColor, gameState.stackingType)) {
      if (gameState.stackingType === "draw2") {
        setGameMessage("❌ You must play a Draw 2 card to stack, or draw the penalty cards!")
      } else if (gameState.stackingType === "draw4") {
        setGameMessage("❌ You must play a Wild Draw 4 card to stack, or draw the penalty cards!")
      } else {
        setGameMessage("❌ That card cannot be played! It must match the color, number, or symbol.")
      }
      return
    }

    if (card.color === "wild") {
      setSelectedCard(card)
      setShowColorPicker(true)
    } else {
      playCard(card)
    }
  }

  const handleColorChoice = (color: CardColor) => {
    if (selectedCard) {
      playCard(selectedCard, color)
    }
    setShowColorPicker(false)
    setSelectedCard(null)
  }

  const playDrawnCard = () => {
    if (drawnCardThisTurn && canPlayDrawnCard) {
      handleCardClick(drawnCardThisTurn)
    }
  }

  if (!gameState) {
    return (
      <div className="min-h-screen bg-green-800 flex items-center justify-center">
        <div className="text-white text-xl">Setting up your UNO game...</div>
      </div>
    )
  }

  const currentPlayerData = gameState.players[gameState.currentPlayer]
  const topCard = gameState.discardPile[gameState.discardPile.length - 1]

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-800 to-green-600 p-2 md:p-4">
      {/* Header */}
      <div className="flex justify-between items-center mb-4 bg-white/10 rounded-lg p-3">
        <Button variant="ghost" onClick={onExit} className="text-white hover:bg-white/20">
          <ArrowLeft className="mr-2" size={20} />
          Exit
        </Button>

        <div className="text-white text-center">
          <h1 className="text-xl md:text-2xl font-bold">UNO Game - Round {roundNumber}</h1>
          <TurnIndicator currentPlayer={currentPlayerData} />
        </div>

        <div className="flex gap-2">
          <Button variant="ghost" onClick={() => setShowInstructions(true)} className="text-white hover:bg-white/20">
            <HelpCircle size={20} />
          </Button>
          <Button
            variant="ghost"
            onClick={() => setSoundEnabled(!soundEnabled)}
            className="text-white hover:bg-white/20"
          >
            {soundEnabled ? <Volume2 size={20} /> : <VolumeX size={20} />}
          </Button>
        </div>
      </div>

      {/* Score Board */}
      <ScoreBoard players={gameState.players} scores={scores} targetScore={targetScore} />

      {/* Game Message */}
      {gameMessage && (
        <div className="bg-white/90 rounded-lg p-3 mb-4 text-center max-w-4xl mx-auto">
          <p className="font-medium text-gray-800">{gameMessage}</p>
          {lastAction && <p className="text-sm text-gray-600 mt-1">{lastAction}</p>}
        </div>
      )}

      {/* Game Status */}
      <GameStatus gameState={gameState} />

      {/* Game Board */}
      <div className="flex flex-col items-center space-y-4 md:space-y-6">
        {/* Other Players */}
        <div className="w-full max-w-6xl">
          {gameState.players.map((player, index) => {
            if (index === 0) return null // Skip human player

            return (
              <div key={player.id} className="mb-3">
                <div className="flex items-center justify-between bg-white/10 rounded-lg p-3">
                  <div className="text-white">
                    <span className="font-semibold">{player.name}</span>
                    <span className="ml-2 text-sm opacity-80">{player.hand.length} cards</span>
                    {gameState.currentPlayer === index && (
                      <span className="ml-2 bg-yellow-400 text-black px-2 py-1 rounded text-xs font-bold">PLAYING</span>
                    )}
                    {player.hand.length === 1 && !player.hasCalledUno && (
                      <div className="ml-2 inline-flex items-center">
                        <span className="text-red-300 text-sm animate-pulse mr-2">⚠️ Forgot UNO!</span>
                        <Button
                          size="sm"
                          variant="outline"
                          className="bg-red-500 text-white border-red-600 hover:bg-red-600 text-xs"
                          onClick={() => catchUnoViolation(index, 0)}
                        >
                          Catch!
                        </Button>
                      </div>
                    )}
                    {player.hand.length === 1 && player.hasCalledUno && (
                      <span className="ml-2 text-yellow-300 text-sm">🎯 UNO!</span>
                    )}
                  </div>

                  <div className="flex space-x-1">
                    {Array.from({ length: Math.min(8, player.hand.length) }).map((_, cardIndex) => (
                      <div
                        key={cardIndex}
                        className="w-8 md:w-10 h-10 md:h-14"
                        style={{ marginLeft: cardIndex > 0 ? "-8px" : "0" }}
                      >
                        <UnoCardComponent
                          card={{ color: "wild", type: "wild" }} // Always show card back for opponents
                          showBack={true} // Always show card back
                          size="small"
                          isPlayable={false}
                        />
                      </div>
                    ))}
                    {player.hand.length > 8 && (
                      <div className="text-white text-sm ml-2">+{player.hand.length - 8}</div>
                    )}
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Center Area - Deck and Discard Pile */}
        <div className="flex items-center space-x-4 md:space-x-8">
          {/* Draw Pile */}
          <div className="text-center">
            <div className="relative cursor-pointer hover:scale-105 transition-transform" onClick={drawCard}>
              <UnoCardComponent
                card={{ color: "wild", type: "wild" }} // Dummy card for back image
                showBack={true}
                size="medium"
                isPlayable={true}
              />
              <div className="absolute inset-0 flex flex-col items-center justify-center bg-black/30 rounded-lg">
                <div className="text-white text-xs font-bold">
                  {gameState.stackingType !== "none" && gameState.drawCount > 0 ? `+${gameState.drawCount}` : "DRAW"}
                </div>
                <div className="text-white text-xs mt-1">{gameState.deck.length}</div>
              </div>
            </div>
            <p className="text-white text-xs mt-2">
              {gameState.stackingType !== "none" && gameState.drawCount > 0 ? "Draw penalty cards" : "Click to draw"}
            </p>
          </div>

          {/* Current Card Info */}
          <div className="text-center bg-white/10 rounded-lg p-3">
            <div className="text-white text-sm font-semibold mb-2">Match This Card</div>
            <UnoCardComponent card={topCard} size="large" />
            <div className="mt-2 text-white text-sm">
              <div>
                Color: <span className="font-bold capitalize">{gameState.currentColor}</span>
              </div>
              {gameState.stackingType !== "none" && gameState.drawCount > 0 && (
                <div className="text-red-300 text-sm font-bold animate-pulse mt-1">
                  ⚠️ STACKING: +{gameState.drawCount} cards
                </div>
              )}
              <div className="text-xs opacity-80 mt-1">
                {gameState.stackingType === "draw2" ? "Play Draw 2 to stack or draw penalty" :
                 gameState.stackingType === "draw4" ? "Play Wild Draw 4 to stack or draw penalty" :
                 "Match color, number, or symbol"}
              </div>
            </div>
          </div>

          {/* Game Controls */}
          <div className="text-center space-y-2">
            {!gameOver && (
              <>
                {/* UNO Catch Button - Available to all players */}
                {gameState.players.some((p, i) => i !== 0 && p.hand.length === 1 && !p.hasCalledUno) && (
                  <Button
                    onClick={() => catchUno(0, gameState.players.findIndex((p, i) => i !== 0 && p.hand.length === 1 && !p.hasCalledUno))}
                    className="bg-red-600 hover:bg-red-700 text-white animate-pulse w-full"
                  >
                    🚨 Catch UNO! 🚨
                  </Button>
                )}

                {gameState.currentPlayer === 0 && (
                  <>
                    {drawnCardThisTurn && canPlayDrawnCard && (
                      <Button onClick={playDrawnCard} className="bg-green-600 hover:bg-green-700 text-white w-full">
                        Play Drawn Card
                      </Button>
                    )}
                    {drawnCardThisTurn && canPlayDrawnCard && (
                      <Button
                        onClick={passTurn}
                        variant="outline"
                        className="bg-white/20 text-white border-white/30 hover:bg-white/30 w-full"
                      >
                        Pass Turn
                      </Button>
                    )}
                  </>
                )}

                <Button
                  onClick={initializeGame}
                  variant="outline"
                  size="sm"
                  className="bg-white/20 text-white border-white/30 hover:bg-white/30 w-full"
                >
                  <RotateCcw size={16} className="mr-1" />
                  New Round
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Human Player Hand */}
        <PlayerHand
          player={gameState.players[0]}
          isCurrentPlayer={gameState.currentPlayer === 0}
          onCardClick={handleCardClick}
          onUnoCall={() => callUno(0)}
          topCard={topCard}
          currentColor={gameState.currentColor}
          drawnCard={drawnCardThisTurn}
        />
      </div>

      {/* Color Picker Modal */}
      {showColorPicker && (
        <ColorPicker
          onColorSelect={handleColorChoice}
          onClose={() => {
            setShowColorPicker(false)
            setSelectedCard(null)
          }}
        />
      )}

      {/* Challenge Modal */}
      {showChallenge && challengeContext && (
        <ChallengeModal
          challengerName={gameState.players[challengeContext.challengerId].name}
          playerName={gameState.players[challengeContext.playerId].name}
          onChallenge={handleChallenge}
        />
      )}

      {/* Stacking Modal */}
      {showStacking && stackingContext && (
        <StackingModal
          playerName={gameState.players[stackingContext.playerId].name}
          stackType={stackingContext.stackType}
          currentPenalty={stackingContext.currentPenalty}
          hasStackCard={gameState.players[stackingContext.playerId].hand.some(card =>
            stackingContext.stackType === "draw2" ? card.type === "draw2" : card.type === "wild-draw4"
          )}
          onStack={() => handleStacking(true)}
          onDraw={() => handleStacking(false)}
        />
      )}

      {/* Instructions Modal */}
      {showInstructions && <GameInstructions onClose={() => setShowInstructions(false)} />}

      {/* Game Over Modal */}
      {gameOver && winner && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="p-6 max-w-md w-full">
            <h2 className="text-2xl font-bold mb-4">Game Over!</h2>
            <p className="text-gray-600 mb-6">{winner.name} wins the game!</p>
            <Button onClick={onExit} className="w-full">
              Back to Menu
            </Button>
          </Card>
        </div>
      )}
    </div>
  )
}
