"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { AlertTriangle } from "lucide-react"

interface ChallengeModalProps {
  challengerName: string
  playerName: string
  onChallenge: (challenge: boolean) => void
}

export default function ChallengeModal({ challengerName, playerName, onChallenge }: ChallengeModalProps) {
  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="max-w-md w-full p-6">
        <div className="text-center mb-6">
          <AlertTriangle className="mx-auto mb-4 text-yellow-500" size={48} />
          <h2 className="text-2xl font-bold mb-2">Wild Draw Four Challenge!</h2>
          <p className="text-gray-600">
            {challengerName}, do you want to challenge {playerName}'s Wild Draw Four card?
          </p>
        </div>

        <div className="bg-yellow-50 p-4 rounded-lg mb-6 border-l-4 border-yellow-400">
          <h3 className="font-semibold text-yellow-800 mb-2">Challenge Rules:</h3>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• Wild +4 can only be played if the player has no cards matching the current color</li>
            <li>• If challenge succeeds: {playerName} draws 4 cards instead</li>
            <li>• If challenge fails: {challengerName} draws 6 cards (4 + 2 penalty)</li>
          </ul>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <Button onClick={() => onChallenge(true)} className="bg-red-600 hover:bg-red-700 text-white">
            Challenge!
          </Button>
          <Button onClick={() => onChallenge(false)} variant="outline">
            Don't Challenge
          </Button>
        </div>
      </Card>
    </div>
  )
}
