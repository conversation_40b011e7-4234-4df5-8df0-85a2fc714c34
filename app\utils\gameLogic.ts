import type { UnoCard, CardColor } from "../types/game"

export function createDeck(): UnoCard[] {
  const deck: UnoCard[] = []
  const colors: CardColor[] = ["red", "blue", "green", "yellow"]

  // Add number cards (0-9)
  colors.forEach((color) => {
    // One 0 card per color
    deck.push({ color, type: "number", value: 0 })

    // Two of each number 1-9 per color
    for (let i = 1; i <= 9; i++) {
      deck.push({ color, type: "number", value: i })
      deck.push({ color, type: "number", value: i })
    }

    // Two of each action card per color
    deck.push({ color, type: "skip" })
    deck.push({ color, type: "skip" })
    deck.push({ color, type: "reverse" })
    deck.push({ color, type: "reverse" })
    deck.push({ color, type: "draw2" })
    deck.push({ color, type: "draw2" })
  })

  // Add wild cards (4 of each)
  for (let i = 0; i < 4; i++) {
    deck.push({ color: "wild", type: "wild" })
    deck.push({ color: "wild", type: "wild-draw4" })
  }

  return deck
}

export function shuffleDeck(deck: UnoCard[]): UnoCard[] {
  const shuffled = [...deck]
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }
  return shuffled
}

export function canPlayCard(card: UnoCard, topCard: UnoCard, currentColor: CardColor): boolean {
  // Wild cards can always be played
  if (card.color === "wild") return true

  // Match color
  if (card.color === currentColor) return true

  // Match number or type
  if (card.type === topCard.type) return true
  if (card.type === "number" && topCard.type === "number" && card.value === topCard.value) return true

  return false
}

export function isValidWildDrawFour(hand: UnoCard[], currentColor: CardColor): boolean {
  // Wild Draw Four can only be played if player has no cards matching current color
  // If they have any card of the same color as the current color, it's invalid
  return !hand.some((card) => card.color === currentColor)
}

export function canChallengeWildDrawFour(playerHand: UnoCard[], currentColor: CardColor): boolean {
  // A challenge is successful if the player who played +4 has cards matching the current color
  // This function checks if the player has cards of the current color (making the +4 illegal)
  return playerHand.some((card) => card.color === currentColor)
}

export function getValidMoves(hand: UnoCard[], topCard: UnoCard, currentColor: CardColor): UnoCard[] {
  return hand.filter((card) => canPlayCard(card, topCard, currentColor))
}

export function calculateScore(hand: UnoCard[]): number {
  return hand.reduce((score, card) => {
    if (card.type === "number") return score + (card.value || 0)
    if (card.type === "wild" || card.type === "wild-draw4") return score + 50
    return score + 20 // Action cards (skip, reverse, draw2)
  }, 0)
}

export function hasMatchingColor(hand: UnoCard[], color: CardColor): boolean {
  return hand.some((card) => card.color === color)
}

export function getCardsByColor(hand: UnoCard[], color: CardColor): UnoCard[] {
  return hand.filter((card) => card.color === color)
}

export function getActionCards(hand: UnoCard[]): UnoCard[] {
  return hand.filter((card) => ["skip", "reverse", "draw2"].includes(card.type))
}

export function getWildCards(hand: UnoCard[]): UnoCard[] {
  return hand.filter((card) => card.color === "wild")
}
