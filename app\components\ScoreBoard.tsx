import type { Player } from "../types/game"
import { Card } from "@/components/ui/card"

interface ScoreBoardProps {
  players: Player[]
  scores: number[]
  targetScore: number
}

export default function ScoreBoard({ players, scores, targetScore }: ScoreBoardProps) {
  return (
    <Card className="mb-4 p-4 bg-white/10 border-white/20">
      <div className="text-white text-center mb-3">
        <h3 className="font-semibold">Score Board (First to {targetScore} wins)</h3>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {players.map((player, index) => (
          <div key={player.id} className="text-center">
            <div className="text-white font-semibold">{player.name}</div>
            <div className="text-yellow-300 text-lg font-bold">{scores[index] || 0}</div>
            <div className="w-full bg-white/20 rounded-full h-2 mt-1">
              <div
                className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(100, ((scores[index] || 0) / targetScore) * 100)}%` }}
              />
            </div>
          </div>
        ))}
      </div>
    </Card>
  )
}
