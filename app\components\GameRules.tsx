"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { ArrowLeft } from "lucide-react"

interface GameRulesProps {
  onClose: () => void
}

export default function GameRules({ onClose }: GameRulesProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-500 to-purple-600 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center mb-6">
          <Button variant="ghost" onClick={onClose} className="text-white hover:bg-white/20 mr-4">
            <ArrowLeft className="mr-2" size={20} />
            Back
          </Button>
          <h1 className="text-3xl font-bold text-white">Official UNO Rules</h1>
        </div>

        <div className="grid gap-6">
          <Card className="p-6">
            <h2 className="text-2xl font-bold mb-4 text-blue-600">Game Setup</h2>
            <ul className="space-y-2 text-gray-700">
              <li>• Each player starts with 7 cards</li>
              <li>• Remaining cards form the draw pile</li>
              <li>• Top card of draw pile starts the discard pile</li>
              <li>• Player with highest card goes first</li>
            </ul>
          </Card>

          <Card className="p-6">
            <h2 className="text-2xl font-bold mb-4 text-green-600">Card Types</h2>
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-semibold mb-2">Number Cards (0-9)</h3>
                <p className="text-sm text-gray-600 mb-4">
                  Each color has cards numbered 0-9. One 0 per color, two of each 1-9.
                </p>

                <h3 className="font-semibold mb-2">Action Cards</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>
                    <strong>Skip:</strong> Next player loses turn
                  </li>
                  <li>
                    <strong>Reverse:</strong> Direction changes
                  </li>
                  <li>
                    <strong>Draw Two:</strong> Next player draws 2
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Wild Cards</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>
                    <strong>Wild:</strong> Change color
                  </li>
                  <li>
                    <strong>Wild Draw Four:</strong> Change color + next player draws 4
                  </li>
                </ul>

                <div className="mt-4 p-3 bg-yellow-50 rounded border-l-4 border-yellow-400">
                  <p className="text-sm text-yellow-800">
                    <strong>Wild Draw Four Rule:</strong> Can only be played if you have no cards matching the current
                    color.
                  </p>
                </div>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <h2 className="text-2xl font-bold mb-4 text-red-600">UNO Call Rules</h2>
            <div className="space-y-4">
              <div className="p-4 bg-red-50 rounded-lg border-l-4 border-red-500">
                <h3 className="font-semibold text-red-800 mb-2">When to Call UNO</h3>
                <p className="text-red-700">
                  You MUST call "UNO" when you play your second-to-last card (leaving you with 1 card).
                </p>
              </div>

              <div className="p-4 bg-orange-50 rounded-lg border-l-4 border-orange-500">
                <h3 className="font-semibold text-orange-800 mb-2">Penalty for Forgetting</h3>
                <p className="text-orange-700">If caught not calling UNO, you must draw 2 penalty cards.</p>
              </div>

              <div className="p-4 bg-green-50 rounded-lg border-l-4 border-green-500">
                <h3 className="font-semibold text-green-800 mb-2">Catching Other Players</h3>
                <p className="text-green-700">
                  You can catch other players who forget to call UNO before the next player starts their turn.
                </p>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <h2 className="text-2xl font-bold mb-4 text-purple-600">Winning & Scoring</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold mb-3">How to Win</h3>
                <ul className="space-y-2 text-gray-700">
                  <li>• First player to play all cards wins the round</li>
                  <li>• Winner scores points based on cards left in opponents' hands</li>
                  <li>• First to reach 500 points wins the game</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold mb-3">Card Values</h3>
                <ul className="space-y-2 text-gray-700">
                  <li>• Number cards: Face value (0-9 points)</li>
                  <li>• Action cards: 20 points each</li>
                  <li>• Wild cards: 50 points each</li>
                </ul>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <h2 className="text-2xl font-bold mb-4 text-indigo-600">Strategy Tips</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold mb-3 text-green-600">Offensive Strategy</h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li>• Play action cards to disrupt opponents</li>
                  <li>• Save Wild cards for crucial moments</li>
                  <li>• Try to play cards that match your hand's colors</li>
                  <li>• Use Draw Two and Skip cards strategically</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold mb-3 text-blue-600">Defensive Strategy</h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li>• Watch other players' card counts</li>
                  <li>• Remember what colors they can't play</li>
                  <li>• Challenge Wild Draw Four cards when suspicious</li>
                  <li>• Don't forget to call UNO!</li>
                </ul>
              </div>
            </div>
          </Card>
        </div>

        <div className="mt-8 text-center">
          <Button onClick={onClose} size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
            Ready to Play!
          </Button>
        </div>
      </div>
    </div>
  )
}
